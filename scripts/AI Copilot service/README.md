本AI及文档来自 github@NB-Group
# AI Copilot 后端介绍
## 介绍
AI Copilot 是一个基于 AI 的文本生成工具，其基于 LLAMA 模型，可以生成文本。
本 AI 部署在 Cloudflare 平台提供的 Worker AI 服务上，其接受来自前端页面的请求，并返回生成文本。

但 Cloudflare 部署的 AI 服务，目前会被限制，无法直接访问，因此，需要使用VPN才能访问。
我在pythonanywhere上部署了一个转发程序，将请求转发到 Cloudflare 上。

Cloudflare AI 服务器 具体的代码可以在 [这里](Cloudflare%20AI.js) 查看

我对该转发服务器的DNS情况进行了分析，在全国各个省市、运营商共56个测试节点的表现如下：
广东广州(最快):9ms，贵州贵阳(最慢):327ms,平均响应:49.982ms

详细信息：
检测点 | 解析时间 | 解析内容
-- | -- | --
北京市 | 27ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
天津市 | 18ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
天津市 | 34ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
上海市 | 76ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
上海市 | 35ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
上海市 | 17ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
广东深圳 | 16ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
广东广州 | 27ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
广东广州 | 9ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
江苏苏州 | 222ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
江苏苏州 | 83ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
四川成都 | 67ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
四川成都 | 40ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
重庆市 | 54ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
重庆市 | 41ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
重庆市 | 46ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
浙江金华 | 20ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
浙江杭州 | 69ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
浙江杭州 | 180ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
山东枣庄 | 74ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
山东青岛 | 31ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
山东济南 | 40ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
黑龙江哈尔滨 | 49ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
黑龙江哈尔滨 | 10ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
湖北武汉 | 27ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
河南郑州 | 38ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
湖南郴州 | 37ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
湖南长沙 | 48ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
河北石家庄 | 38ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
河北石家庄 | 50ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
吉林长春 | 11ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
安徽芜湖 | 17ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
江西九江 | 33ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
内蒙古呼和浩特 | 18ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
内蒙古呼和浩特 | 33ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
内蒙古 | 96ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
内蒙古 | 49ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
陕西西安 | 58ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
陕西西安 | 32ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
广西南宁 | 13ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
云南昆明 | 49ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
贵州贵阳 | 327ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
贵州贵阳 | 45ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
贵州贵阳 | 9ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
海南海口 | 39ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
福建厦门 | 28ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
福建宁德 | 18ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
福建福州 | 34ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
新疆乌鲁木齐 | 69ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
甘肃兰州 | 36ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
青海西宁 | 10ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
西藏拉萨 | 66ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
宁夏中卫 | 11ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
山西太原 | 9ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
辽宁沈阳 | 9ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]
辽宁沈阳 | 157ms | 35.173.69.207[弗吉尼亚阿什本亚马逊云]

![image](https://github.com/tjy-gitnub/win12/assets/121747915/a49a750e-210f-4cb1-b236-adcdd94eb1c9)

