# 翻译贡献指南
暂时**只需英语**，其他语言待完善后再吧。

开始翻译前，**务必联系我们，并征得同意**，以了解是否还有其他人正在翻译，避免重复劳动和合并冲突。

## 条件
1. 有中文基础
2. 有一定的目标语言的基础 / 愿意将自己的 Windows 11 系统调成目标语言
3. 有代码基础

## 方法
项目使用 i18n 库实现多语言，可上网查阅，或自行研究已有翻译的部分

### 已有成果
0. 务必理解 `desktop.js` 开头语言处理部分，熟悉原理
1. 此目录下 `trans.py`，有 Python 基础者自行研究
2. `desktop.js` 中有 `lang(txt,id)`，详见注释

### 注意
部分代码形如
```html
<a class="btn">
    <i class="bi"></i> 文本
</a>
```
可能不便于设置属性，可对纯文本部分添加 `<span>` 标签，例可改为

```html
<a class="btn">
    <i class="bi"></i>
    <span data-i18n="sth">文本</span>
</a>
```

顺便把 `lang_zh_cn` 文件一同完善，感激不尽。

### 加分项
1. 与 Windows 11 一致的部分，应符合(尽量)最新版的原生语言内容
2. 不一致的，应避免机器翻译

## 待翻译的内容
1. 设置除“个性化”外的各个页面
2. 任务管理器
3. js 代码部分，如右键菜单、下拉菜单和notice
4. html 中元素的 win12_title 属性

## 酬劳
有且仅有
1. Github 贡献点
2. 来自开发者的由衷感谢 >u-)o