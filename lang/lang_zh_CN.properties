updating=正在更新
cover-hp=横屏以获得最佳体验
login=登录
welcome=欢迎
stmenu-avlb=可用
stmenu-webapp=Web 应用
bilibili.name=哔哩哔哩
wsa.name=适用于 Android™ 的 Windows 子系统
folder.doc=文档
folder.pic=图片
folder.mus=音乐
stmenu-pinned=已固定
stmenu-allapp=所有应用
feedback.name=反馈中心
stmenu-tj=推荐的项目
stmenu-tj-f1=科学地使用瓶盖.pptx
stmenu-tj-t1=5 分钟前
stmenu-tj-f2=可口可乐瓶盖.jpg
stmenu-tj-t2=7 分钟前
stmenu-tj-f3=瓶盖构造图.jpg
stmenu-tj-t3=16 分钟前
stmenu-tj-f4=瓶盖的构造及作用.docx
stmenu-tj-t4=24 分钟前
stmenu-tj-f5=可口可乐瓶盖厚度.xlsx
stmenu-tj-t5=35 分钟前
sch-app=应用
sch-doc=文档
sch-web=网页
sch-setting=设置
sch-folder=文件夹
sch-photo=照片
sch-tj=推荐
sch-open=打开
sch-opfl=打开文件所在位置
sch-cpp=复制路径
sch-ph=在这里输入你要搜索的内容
widget=小组件
widget-news=新闻
widget-news-res=我们不对新闻内容负责
ctrl-blue=蓝牙
ctrl-air=飞行模式
ctrl-eye=护眼
ctrl-hotspot=移动热点
ctrl-acc=辅助功能
data-week=<p>一</p><p>二</p><p>三</p><p>四</p><p>五</p><p>六</p><p>日</p>
editmd-add=添加小组件

psnl=个性化
home=主页
apply=应用
ok=确定
cancel=取消
all=全部
more=更多
close=关闭
refresh=刷新
open=打开
del=删除

desktop.tgltheme=切换主题
desktop.vogithub=在 Github 中查看此项目
desktop.exitedit=退出编辑模式 
desktop.enteredit=进入编辑模式

nts.about=<p class="tit">Windows 12 网页版</p><p>Windows 12 网页版是一个开放源项目,<br />希望让用户在网络上预先体验 Windows 12,<br />内容可能与 Windows 12 正式版本不一致。<br />使用标准网络技术,例如 HTML, CSS 和 JS<br />此项目绝不附属于微软,且不应与微软操作系统或产品混淆,<br />这也不是 Windows365 cloud PC<br />本项目中微软、Windows和其他示范产品是微软公司的商标<br />本项目中 Android 是谷歌公司的商标。</p>
nts.feedback.name=反馈
nts.feedback.txt=我们非常注重用户的体验与反馈
nts.feedback.github=在github上提交issue (需要github账户)
nts.addwg=添加小组件
nts.addwg.weather=天气
nts.addwg.monitor=系统性能监视器

window.restore=还原
window.min=最小化
window.max=最大化
window.close=关闭

# calc
calc.name=计算器
calc.error.zero=<p class="tit">错误</p><p>除数不得等于0</p>

# defender
defender.name=Windows 安全中心
defender.virus=病毒威胁与防护
defender.account=账户防护
defender.firewall=防火墙和网络防护
defender.device=设备安全性
defender.glance=安全性概览
defender.network=网络流量使用
defender.speed=网络速度
defender.clearv=入侵病毒清除
defender.allvc=所有病毒均被清除
defender.freq=电脑受病毒攻击频率
defender.netreq=网络通信比例
defender.netreq-thismonth=这个月
defender.recreq=请求记录

# msstore
msstore.apps=应用
msstore.games=游戏
msstore.download=下载
msstore.topfree=热门免费应用 >
msstore.free=免费下载
msstore.devtool=开发
msstore.genimp=原神
msstore.game=游戏
msstore.mc=我的世界
msstore.design=设计
msstore.tool=工具
msstore.business=办公

# setting
setting.name=设置
setting.usrname=星瓶
setting.sch=查找设置

setting.system=系统
setting.device=蓝牙和其他设备
setting.network=网络和 Internet

setting.psnl=个性化
setting.psnl.color=颜色
setting.psnl.color-dt=设置 Windows 的主题色
setting.psnl.color.now=当前颜色
setting.psnl.color.custom=自定义颜色
setting.psnl.theme=主题
setting.psnl.theme-dt=(！消耗大量 github api 限度！) 设置 Windows 的主题 想要<span class="a jump" win12_title="https://github.com/tjy-gitnub/win12-theme" onclick="window.open('https://github.com/tjy-gitnub/win12-theme','_blank')">上传自己的主题</span>?
setting.psnl.round=圆角
setting.psnl.round-dt=设置系统界面中是否启用圆角
setting.psnl.animation=动画
setting.psnl.animation-dt=系统界面元素过渡动画
setting.psnl.shadow=阴影
setting.psnl.shadow-dt=为系统界面元素添加阴影效果
setting.psnl.alltransp=多窗口透明 
setting.psnl.alltransp-dt=为所有窗口开启透明效果，而不是仅用于焦点窗口 <span class="a jump" win12_title="开启后将占用大量GPU,可能造成卡顿">详细</span>
setting.psnl.mica=Mica效果 (实验)
setting.psnl.mica-dt=为焦点窗口开启mica效果 <a class="a jump" onclick="shownotice('feedback')">反馈</a>
setting.psnl.startup-sound=开机音乐
setting.psnl.startup-sound-dt=是否启用开机音乐
setting.psnl.ball=语音输入球
setting.psnl.ball-dt=是否启用语音输入球

setting.apps=应用
setting.accounts=账户
setting.timelang=时间和语言
setting.game=游戏
setting.acc=辅助功能
setting.privacy=隐私和安全性
setting.update=Windows 更新

# edge
edge.schbing=在必应中搜索，或输入一个网址
edge.rename=为标签页命名

# explorer
explorer.name=文件资源管理器
explorer.pinned=已固定
explorer.quickacc=快速访问
explorer.thispc=此电脑
explorer.bin=回收站
explorer.tag=标签
explorer.tag.red=红色
explorer.tag.blue=蓝色
explorer.tag.yellow=黄色
explorer.tag.green=绿色
explorer.tag.orange=橙色
explorer.tag.purple=紫色
explorer.tag.pink=粉色
explorer.new=新建
explorer.sort=排序方式
explorer.view=布局

# about
about.name=关于 Win12 网页版
about.intro=简介
about.update=更新记录
about.intro.name=<span></span>Windows 12 网页版
about.intro.intro=<span></span>简介
about.intro.intro.p1=&emsp;&emsp;Windows 12 网页版是一个开源项目，由星源原创，使用 HTML、CSS 和 JavaScript，在网络上模拟、创新操作系统。
about.intro.intro.p2=&emsp;&emsp;此项目已发布至 GitHub，<a onclick="window.open('https://github.com/tjy-gitnub/win12','_blank');" win12_title="https://github.com/tjy-gitnub/win12" class="jump">点击此处查看</a>。
about.intro.intro.p3=&emsp;&emsp;若您对于该项目有任何意见或建议，请在 GitHub 上<a onclick="window.open('https://github.com/tjy-gitnub/win12/issues','_blank');" win12_title="https://github.com/tjy-gitnub/win12/issues" class="jump">提交 issues</a>，您的问题会被尽可能快地解决。
about.intro.intro.p4=&emsp;&emsp;原创作者星源，点击以<a onclick="window.open('https://tjy-gitnub.github.io/','_blank');" win12_title="https://tjy-gitnub.github.io/" class="jump">了解详细</a>。
about.intro.os=<span></span>开源说明
about.intro.os.p1=&emsp;&emsp;此项目是一个开源项目。此项目使用 EPL v2.0 开源许可。开源许可是具有法律效力的合同，请自觉遵守开源许可，尊重他人劳动。
about.intro.os.p2=&emsp;&emsp;根据许可，你可以对该项目进行传播、分发、修改以及二次发布，包括个人和商业用途。
about.intro.os.p3=&emsp;&emsp;但您必须给出源码来源，<strong>包括作者，项目链接等</strong>，必须使用相同的协议开源。
about.intro.os.p4=&emsp;&emsp;不是在该项目基础上进行增加、修改的，仅参考源码的，不需要开源，但也仅供学习用途。
about.intro.thank=<span></span>特别感谢
about.intro.thank.p1=&emsp;&emsp;特别感谢 @NB-Group，他为本项目的后端开发做出了重要贡献！
about.intro.contri=<span></span>贡献者
about.intro.contri.p1=&emsp;&emsp;感谢所有项目的贡献者（实时数据，计算方法：即为 Commits 次数）
about.intro.star.p1=&emsp;&emsp;感谢<a class="jump" win12_title="给个 Star 好不好？(即将跳转到：https://github.com/tjy-gitnub/win12/stargazers)" onclick="window.open('https://github.com/tjy-gitnub/win12/stargazers','_blank')">所有支持我们的人</a>。
about.intro.others=<span></span>其它
about.intro.others.p1=&emsp;&emsp;此项目基于目前 Windows 版本创造，与微软的 Windows 12 正式版本不一致。
about.intro.others.p2=&emsp;&emsp;此项目绝不附属于微软，且不应与微软操作系统或产品混淆，这也不是 Windows365 cloud PC。
about.intro.others.p3=&emsp;&emsp;本项目中微软、Windows 和其他示范产品是微软公司的商标。本项目中 Android 是谷歌公司的商标。
about.update.name=<span></span>更新记录

# notepad
notepad.name=记事本
notepad.file=文件
notepad.edit=编辑
notepad.format=格式

# terminal
terminal.name=终端

# camera
camera.name=相机
camera.notice=提示
camera.notice.txt=&emsp;&emsp;欢迎使用“相机”应用！本应用由 User782Tec 开发， tjy-gitnub 维护。<br>&emsp;&emsp;在使用中，此应用将会获取访问您摄像头的权限（用于进行拍摄照片等操作）。此应用不会将任何您的个人信息（包括所拍摄的照片）上传至服务器，更不会泄露给他人。&emsp;&emsp;若您不同意上述协议，则将不能使用此应用。
camera.notice.agree=同意并继续
camera.notice.disag=不同意

# run
run.name=运行
run.txt=Windows 将根据你所输入的名称，为你打开相应的程序、文件夹、文档或 Internet 资源。
run.open=打开: 

# winver
winver.name=关于 Windows 
winver.p1=版本 25H2 (OS 内部版本 24612.1896)
winver.p2=© Microsoft Corporation。保留所有权利。
winver.p3=Windows12 专业版操作系统及其用户界面受美国和其他国家/地区的商标法和其他待颁布或已颁布的知识产权法保护。
winver.p4=根据Microsoft软件许可条款，许可如下用户使用本产品
winver.p5=上面都是胡扯，切勿当真(双击隐藏此消息)

# taskmgr
taskmgr.name=任务管理器

# copilot
copilot.start=开始对话
copilot.p=使用前请先点击右上 "<i class="bi bi-info-circle"></i>" 查看使用注意事项
copilot.send=发送
