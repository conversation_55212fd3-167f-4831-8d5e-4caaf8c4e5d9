updating=Updating
cover-hp=Landscape for the best experience
login=Login
welcome=Welcome
stmenu-avlb=Available
stmenu-webapp=Webapps
bilibili.name=Bilibili
wsa.name=Windows Subsystem for Android™
folder.doc=Documents
folder.pic=Pictures
folder.mus=Music
stmenu-pinned=Pinned
stmenu-allapp=All
feedback.name=Feedback Hub
stmenu-tj=Recommended
stmenu-tj-f1=How to use bottle caps scientifically.pptx
stmenu-tj-t1=5 minutes ago
stmenu-tj-f2=Coca Cola bottle cap.jpg
stmenu-tj-t2=7 minutes ago
stmenu-tj-f3=Bottle cap construction diagram.jpg
stmenu-tj-t3=16 minutes ago
stmenu-tj-f4=The structure and function of bottle caps.docx
stmenu-tj-t4=24 minutes ago
stmenu-tj-f5=The thickness of Coca Cola bottle caps.xlsx
stmenu-tj-t5=35 minutes ago
sch-app=Apps
sch-doc=Documents
sch-web=Web
sch-setting=Settings
sch-folder=Folders
sch-photo=Photos
sch-tj=Recommended
sch-open=Open
sch-opfl=Open file location
sch-cpp=Copy path
sch-ph=Type here to serach
widget=Widgets
widget-news=News
widget-news-res=We are not responsible for news content.
ctrl-blue=Bluetooth
ctrl-air=Airplane mode
ctrl-eye=Eye protection
ctrl-hotspot=Mobile hotspot
ctrl-acc=Accessibility
data-week=<p>Mon</p><p>Tue</p><p>Wed</p><p>Thu</p><p>Fri</p><p>Sat</p><p>Sun</p>
editmd-add=Add widget


psnl=Personalize
home=Home
apply=Apply
ok=OK
cancel=Cancel
all=All
more=More
close=Close
refresh=Refresh
open=Open
del=Delete

desktop.tgltheme=Toggle color mode
desktop.vogithub=View on Github
desktop.exitedit=Exit editing mode
desktop.enteredit=Enter editing mode

nts.about=<p class="tit">Windows 12 Online</p><p>Windows 12 Online is an open source project,<br>which aims to provide users with a pre experience of Windows 12 online<br />It is not consistent with official version of Windows 12.<br />Using standard network technologies such as HTML, CSS and JS.<br />This project is not affiliated with Microsoft and should not be<br> confused with Microsoft products.<br> It is also not Windows365 cloud PC.<br>Microsoft, Windows, and other demonstration products in this project<br> are trademarks of Microsoft Corporation.<br />Android in this project is a trademark of Google Inc.</p>
nts.feedback.name=Feedback
nts.feedback.txt=We place great emphasis on user experience and feedback.
nts.feedback.github=Submit an issue on GitHub (requires a GitHub account)
nts.addwg=Add a widget
nts.addwg.weather=Weather
nts.addwg.monitor=Resource monitor


window.restore=Restore
window.min=Minimize
window.max=Maximize
window.close=Close

# calc
calc.name=Calculator
calc.error.zero=<p class="tit">Error</p><p>The divisor cannot be 0</p>


# defender
defender.name=Windows Security
defender.virus=Virus & threat protection
defender.account=Account protection
defender.firewall=Firewall & network protection
defender.device=Device security
defender.glance=Security at a glance
defender.network=Network usage
defender.speed=Network speed
defender.clearv=Intrusion virus clearance
defender.allvc=All viruses have been cleared.
defender.freq=Frequency of virus attacks
defender.netreq=Network requests
defender.netreq-thismonth=This month
defender.recreq=Request record


# msstore
msstore.apps=Apps
msstore.games=Gaming
msstore.download=Downloads
msstore.topfree=Top free apps >
msstore.free=Free
msstore.devtool=Developer tools
msstore.genimp=Genshin Impact
msstore.game=Game
msstore.mc=Minecraft
msstore.design=Multimedia design
msstore.tool=Utilities & tools
msstore.business=Business


# setting
setting.name=Settings
setting.usrname=Starry bottle
setting.sch=Find a setting

setting.system=System
setting.device=Bluetooth & devices
setting.network=Network & internet

setting.psnl=Personalization
setting.psnl.color=Colors
setting.psnl.color-dt=Set accent color in Windows
setting.psnl.color.now=Now
setting.psnl.color.custom=Custom color
setting.psnl.theme=Themes
setting.psnl.theme-dt=(!Consuming a large amount of GitHub API limits!) Set the theme for Windows. Do you want to <span class="a jump" win12_title="https://github.com/tjy-gitnub/win12-theme" onclick="window.open('https://github.com/tjy-gitnub/win12-theme','_blank')">upload your own theme</span>?
setting.psnl.round=Rounded corner
setting.psnl.round-dt=Set whether to enable rounded corners in Windows.
setting.psnl.animation=Animation effects
setting.psnl.animation-dt=Set whether to enable animation effects
setting.psnl.shadow=Shadow
setting.psnl.shadow-dt=Set whether to enable shadow effect
setting.psnl.alltransp=Transparency effects for all windows
setting.psnl.alltransp-dt=Enable transparency effects for all windows, not just for the focus window <span class="a jump" win12_title="It will occupy a large amount of GPU and may cause lag">For details</span>
setting.psnl.mica=Mica effects (Beta)
setting.psnl.mica-dt=Enable mica effects for the focus window
setting.psnl.startup-sound=Sound effect after startup
setting.psnl.startup-sound-dt=Set whether to play the sound effect after startup
setting.psnl.ball=Voice input ball
setting.psnl.ball-dt=Set whether to enable the voice input ball

setting.apps=Apps
setting.accounts=Accounts
setting.timelang=Time & Languages
setting.game=Gaming
setting.acc=Accessibility
setting.privacy=Privacy & security
setting.update=Windows Update


# edge
edge.schbing=Serach in Bing or enter an address
edge.rename=Rename this tab


# explorer
explorer.name=File Explorer
explorer.pinned=Pinned
explorer.quickacc=Quick access
explorer.thispc=This PC
explorer.bin=Recycle Bin
explorer.tag=Tags
explorer.tag.red=Red
explorer.tag.blue=Blue
explorer.tag.yellow=Yellow
explorer.tag.green=Green
explorer.tag.orange=Orange
explorer.tag.purple=Purple
explorer.tag.pink=Pink
explorer.new=New
explorer.sort=Sort
explorer.view=View


# about
about.name=About Win12 Online
about.intro=Introduction
about.update=Update log
about.intro.name=<span></span>Windows 12 Online
about.intro.intro=<span></span>Introduction
about.intro.intro.p1=Windows 12 Online is an open source project, developed by Starry Source. It use HTML, CSS and JS to simulate and innovate Windows 12 online.
about.intro.intro.p2=This project has been published on GitHub, <a onclick="window.open('https://github.com/tjy-gitnub/win12','_ blank');" win12_title=" https://github.com/tjy-gitnub/win12 " class="jump">Click here to view</a>.
about.intro.intro.p3=If you have any comments or suggestions, please <a onclick="window.open('https://github.com/tjy-gitnub/win12/issues','_blank');" win12_title="https://github.com/tjy-gitnub/win12/issues" class="jump">send issues on Github</a>, your problem will be solved as soon as possible.
about.intro.intro.p4=About the author, Starry Source, click for <a onclick="window.open('https://tjy-gitnub.github.io/','_blank');" win12_title="https://tjy-gitnub.github.io/" class="jump">more information</a>.
about.intro.os=<span></span>Open Source License
about.intro.os.p1=It is an open source project, using the EPL v2.0 licence. Open source licence is a contract with legal effect. Please consciously abide by it and respect the labor of others.
about.intro.os.p2=According to the licence, you can disseminate, distribute, modify and re publish the project, including personal and commercial purposes.
about.intro.os.p3=But you must indicate the source of the code, <strong>including the author, project link, etc.</strong>, and the code must be open source.
about.intro.os.p4=And if it is not added or modified on the basis of the project, it does not need open source. But also only for learning purposes.
about.intro.thank=<span></span>Thanks
about.intro.thank.p1=Special thanks to @NB-Group, who has made important contributions to the back-end development of this project!
about.intro.contri=<span></span>Contributors
about.intro.contri.p1=Thanks to all project contributors (real-time data, by the number of Commits)
about.intro.star.p1=Thank you to all those <a class="jump" win12_title="How about join in this list? (Jumping to: https://github.com/tjy-gitnub/win12/stargazers)" onclick="window.open('https://github.com/tjy-gitnub/win12/stargazers','_blank')">who have supported us</a>.
about.intro.others=<span></span>Others
about.intro.others.p1=This project is based on the current version of Windows and is not consistent with official version of Windows 12.
about.intro.others.p2=This project is not affiliated with Microsoft and should not be confused with Microsoft products. It is also not Windows365 cloud PC.
about.intro.others.p3=Microsoft, Windows, and other demonstration products in this project are trademarks of Microsoft Corporation. Android in this project is a trademark of Google Inc.
about.update.name=<span></span>Update log


# notepad
notepad.name=Notepad
notepad.file=File
notepad.edit=Edit
notepad.format=Format


# terminal
terminal.name=Terminal


# camera
camera.name=Camera
camera.notice=Notice
camera.notice.txt=Camera is developed by User782Tec and will be maintained by tjy-gitnub. <br>During use, it will request access to your camera (for taking photos). It won’t upload any of your personal information (including photos taken) to internet, nor will it disclose it to others.<br>If you don’t agree to the above, you will not be able to use Camera.
camera.notice.agree=Agree and continue
camera.notice.disag=Disagree


# run
run.name=Run
run.txt=Type the name of a program, folder, document, or Internet resource, and Windows will open it for you.
run.open=Open


# winver
winver.name=About Windows
winver.p1=Version 25H2 (OS Build 24612.1896)
winver.p2=© Microsoft Corporation. All rights reserved.
winver.p3=The Windows 12 Pro operating system and its user interface are protected by trademark and other pending or existing intellectual property rights in the United States and other countries/regions.
winver.p4=This product is licensed under the Microsoft Software License Terms to:
winver.p5=The above is all nonsense, don’t take it seriously (double-click to hide this message)


# taskmgr
taskmgr.name=Task Manager 

# copilot
copilot.start=Start the conversation
copilot.p=Please click "<i class="bi bi-info-circle"></i>" on the top right to check the precautions before use. This copilot may not communicate in your language.
copilot.send=Send

