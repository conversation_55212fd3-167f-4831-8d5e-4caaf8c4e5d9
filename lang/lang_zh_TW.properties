updating=正在更新
cover-hp=請轉為橫向檢視以獲得最佳體驗
login=登入
welcome=歡迎
stmenu-avlb=可用
stmenu-webapp=Web 應用程式
bilibili.name=嗶哩嗶哩
wsa.name=Windows Subsystem for Android™
folder.doc=檔案
folder.pic=圖片
folder.mus=音樂
stmenu-pinned=已釘選
stmenu-allapp=全部
feedback.name=意見反應中樞
stmenu-tj=建議專案
stmenu-tj-f1=科學地使用瓶蓋.pptx
stmenu-tj-t1=5 分鐘前
stmenu-tj-f2=可口可樂瓶蓋.jpg
stmenu-tj-t2=7 分鐘前
stmenu-tj-f3=瓶蓋構造圖.jpg
stmenu-tj-t3=16 分鐘前
stmenu-tj-f4=瓶蓋的構造及作用.docx
stmenu-tj-t4=24 分鐘前
stmenu-tj-f5=可口可樂瓶蓋厚度.xlsx
stmenu-tj-t5=35 分鐘前
sch-app=應用程式
sch-doc=檔案
sch-web=網頁
sch-setting=設定
sch-folder=資料夾
sch-photo=相片
sch-tj=建議
sch-open=開放
sch-opfl=開放檔案位置
sch-cpp=複製路徑
sch-ph=在這裡輸入以進行搜尋
widget=小工具
widget-news=新聞
widget-news-res=我們不對新聞內容負責
ctrl-blue=藍牙
ctrl-air=飛行模式
ctrl-eye=護眼
ctrl-hotspot=行動熱點
ctrl-acc=協助功能
data-week=<p>一</p><p>二</p><p>三</p><p>四</p><p>五</p><p>六</p><p>日</p>
editmd-add=添加小工具

psnl=個人化
home=首頁
apply=套用
ok=確定
cancel=取消
all=全部
more=更多
close=關閉
refresh=重新整理
open=開啟
del=刪除

desktop.tgltheme=切換佈景主題
desktop.vogithub=在 GitHub 檢視此專案
desktop.exitedit=結束編輯模式
desktop.enteredit=進入編輯模式

nts.about=<p class="tit">Windows 12 網頁版</p><p>Windows 12 網頁版是一個開放源專案，<br />希望讓使用者在網路上預先體驗 Windows 12，<br />內容可能與 Windows 12 正式版本不一致。<br />使用標準網路技術，例如 HTML， CSS 和 JS<br />此專案絕不附屬於微軟，且不應與微軟操作系統或產品混淆，<br />這也不是 Windows365 cloud PC<br />本專案中微軟、Windows和其他示範產品是微軟公司的商標<br />本專案中 Android 是谷歌公司的商標。</p>
nts.feedback.name=意見反應
nts.feedback.txt=我們非常注重使用者的體驗與反應
nts.feedback.github=在github上提交issue (需要github帳戶)
nts.addwg=添加小工具
nts.addwg.weather=天氣
nts.addwg.monitor=系統性能監視器

window.restore=還原
window.min=最小化
window.max=最大化
window.close=關閉

# calc
calc.name=小算盤
calc.error.zero=<p class="tit">錯誤</p><p>除數不得等於0</p>

# defender
defender.name=Windows 安全性
defender.virus=病毒與威脅防護
defender.account=帳戶防護
defender.firewall=防火牆和網路保護
defender.device=裝置安全性
defender.glance=安全性概覽
defender.network=網路流量使用
defender.speed=網路速度
defender.clearv=入侵病毒清除
defender.allvc=所有病毒均被清除
defender.freq=電腦受病毒攻擊頻率
defender.netreq=網路通信比例
defender.netreq-thismonth=這個月
defender.recreq=請求記錄

# msstore
msstore.apps=應用程式
msstore.games=遊戲
msstore.download=下載
msstore.topfree=熱門免費應用程式 >
msstore.free=免費下載
msstore.devtool=開發
msstore.genimp=原神
msstore.game=遊戲
msstore.mc=Minecraft
msstore.design=設計
msstore.tool=工具
msstore.business=商務

# setting
setting.name=設定
setting.usrname=星瓶
setting.sch=尋找設定

setting.system=系統
setting.device=藍牙與裝置
setting.network=網路和網際網路

setting.psnl=個人化
setting.psnl.color=色彩
setting.psnl.color-dt=設定 Windows 的佈景主題色彩
setting.psnl.color.now=目前色彩
setting.psnl.color.custom=自訂色彩
setting.psnl.theme=主題
setting.psnl.theme-dt=(！消耗大量 github api 限度！) 設置 Windows 的主題 想要<span class="a jump" win12_title="https://github.com/tjy-gitnub/win12-theme" onclick="window.open('https://github.com/tjy-gitnub/win12-theme','_blank')">上載自己的主題</span>?
setting.psnl.round=圓角
setting.psnl.round-dt=設置系統界面中是否啟用圓角
setting.psnl.animation=動畫
setting.psnl.animation-dt=系統界面元素過渡動畫
setting.psnl.shadow=陰影
setting.psnl.shadow-dt=為系統界面元素添加陰影效果
setting.psnl.alltransp=多窗口透明 
setting.psnl.alltransp-dt=為所有窗口開啟透明效果，而不是僅用於焦點窗口 <span class="a jump" win12_title="開啟後將佔用大量GPU,可能造成卡頓">詳細</span>
setting.psnl.mica=Mica效果 (實驗)
setting.psnl.mica-dt=為焦點窗口開啟mica效果 <a class="a jump" onclick="shownotice('feedback')">反饋</a>
setting.psnl.startup-sound=開機音樂
setting.psnl.startup-sound-dt=是否啟用開機音樂
setting.psnl.ball=語音輸入球
setting.psnl.ball-dt=是否啟用語音輸入球

setting.apps=應用程式
setting.accounts=帳戶
setting.timelang=時間和語言
setting.game=遊戲
setting.acc=協助工具
setting.privacy=隱私權與安全性
setting.update=Windows Update

# edge
edge.schbing=在必應中搜索，或輸入一個網址
edge.rename=為標籤頁命名

# explorer
explorer.name=檔案總管
explorer.pinned=已釘選
explorer.quickacc=快速存取
explorer.thispc=本機
explorer.bin=資源回收筒
explorer.tag=標籤
explorer.tag.red=紅色
explorer.tag.blue=藍色
explorer.tag.yellow=黃色
explorer.tag.green=綠色
explorer.tag.orange=橙色
explorer.tag.purple=紫色
explorer.tag.pink=粉色
explorer.new=新增
explorer.sort=排序
explorer.view=檢視

# about
about.name=關於 Win12 網頁版
about.intro=簡介
about.update=更新歷程記錄
about.intro.name=<span></span>Windows 12 網頁版
about.intro.intro=<span></span>簡介
about.intro.intro.p1=&emsp;&emsp;Windows 12 網頁版是一個開源專案，由星源創作，使用 HTML、CSS 和 JavaScript，在網絡上模擬、創新操作系統。
about.intro.intro.p2=&emsp;&emsp;此專案已發布至 GitHub，<a onclick="window.open('https://github.com/tjy-gitnub/win12','_blank');" win12_title="https://github.com/tjy-gitnub/win12" class="jump">點擊此處查看</a>。
about.intro.intro.p3=&emsp;&emsp;若您對於該專案有任何意見或建議，請在 GitHub 上<a onclick="window.open('https://github.com/tjy-gitnub/win12/issues','_blank');" win12_title="https://github.com/tjy-gitnub/win12/issues" class="jump">提交 issues</a>，您的問題會被盡可能快地解決。
about.intro.intro.p4=&emsp;&emsp;原創作者星源，點擊以<a onclick="window.open('https://tjy-gitnub.github.io/','_blank');" win12_title="https://tjy-gitnub.github.io/" class="jump">瞭解詳細</a>。
about.intro.os=<span></span>開源說明
about.intro.os.p1=&emsp;&emsp;此專案是一個開源專案。此專案使用 EPL v2.0 開源許可。開源許可是具有法律效力的合同，請自覺遵守開源許可，尊重他人勞動。
about.intro.os.p2=&emsp;&emsp;根據許可，你可以對該專案進行傳播、分發、修改以及二次發布，包括個人和商業用途。
about.intro.os.p3=&emsp;&emsp;但您必須給出源碼來源，<strong>包括作者，專案鏈接等</strong>，必須使用相同的協議開源。
about.intro.os.p4=&emsp;&emsp;不是在該專案基礎上進行增加、修改的，僅參考源碼的，不需要開源，但也僅供學習用途。
about.intro.thank=<span></span>特別感謝
about.intro.thank.p1=&emsp;&emsp;特別感謝 @NB-Group，他為本專案的後端開發做出了重要貢獻！
about.intro.contri=<span></span>貢獻者
about.intro.contri.p1=&emsp;&emsp;感謝所有專案的貢獻者（即時數據，計算方法：即為 Commits 次數）
about.intro.star.p1=&emsp;&emsp;感謝<a class="jump" win12_title="給個 Star 好不好？(即將跳轉到：https://github.com/tjy-gitnub/win12/stargazers)" onclick="window.open('https://github.com/tjy-gitnub/win12/stargazers','_blank')">所有支持我們的人</a>。
about.intro.others=<span></span>其它
about.intro.others.p1=&emsp;&emsp;此專案基於目前 Windows 版本創造，與微軟的 Windows 12 正式版本不一致。
about.intro.others.p2=&emsp;&emsp;此專案絕不附屬於微軟，且不應與微軟操作系統或產品混淆，這也不是 Windows365 cloud PC。
about.intro.others.p3=&emsp;&emsp;本專案中微軟、Windows 和其他示範產品是微軟公司的商標。本專案中 Android 是谷歌公司的商標。
about.update.name=<span></span>更新歷程記錄

# notepad
notepad.name=記事本
notepad.file=檔案
notepad.edit=編輯
notepad.format=檢視

# terminal
terminal.name=終端機

# camera
camera.name=相機
camera.notice=提示
camera.notice.txt=&emsp;&emsp;歡迎使用“相機”應用！本應用由 User782Tec 開發， tjy-gitnub 維護。<br>&emsp;&emsp;在使用中，此應用程式將會獲取訪問您攝像頭的權限（用於進行拍攝相片等操作）。此應用程式不會將任何您的個人信息（包括所拍攝的相片）上傳至服務器，更不會洩露給他人。&emsp;&emsp;若您不同意上述協議，則將不能使用此應用程式。
camera.notice.agree=同意並繼續
camera.notice.disag=不同意

# run
run.name=執行
run.txt=輸入程式、資料夾、文件或網際網路資源的名稱，Windows 會自動開啟。
run.open=開啟

# winver
winver.name=關於 Windows 
winver.p1=版本 25H2 (OS 組建 24612.1896)
winver.p2=© Microsoft Corporation。保留所有權利。
winver.p3=Windows 12 專業版 作業系統及其使用者介面受美國及其他國家/地區之商標及其他已立案和立案中的智慧財產權法保護。
winver.p4=此產品依據 Microsoft 軟體許可條款中規定，使用權屬於：
winver.p5=上面都是胡扯，不可當真(雙擊隱藏此消息)

# taskmgr
taskmgr.name=工作管理員

# copilot
copilot.start=開始對話
copilot.p=使用前請先點擊右上 "<i class="bi bi-info-circle"></i>" 查看使用注意事項。此 Copilot 可能不以你使用的語言回答。
copilot.send=發送
