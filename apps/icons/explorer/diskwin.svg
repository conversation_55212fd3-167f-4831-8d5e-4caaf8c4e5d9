<svg width="259" height="259" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="3.11111 3.11111"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="3.11111 3.11111"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="3.11111 3.11111"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.521569" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="3.11111 3.11111"/></filter><clipPath id="clip4"><rect x="511" y="252" width="259" height="259"/></clipPath><linearGradient x1="623.562" y1="312.59" x2="658.438" y2="408.41" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill5"><stop offset="0" stop-color="#E1E3E6"/><stop offset="0.18" stop-color="#E1E3E6"/><stop offset="0.91" stop-color="#C4C8CE"/><stop offset="1" stop-color="#C4C8CE"/></linearGradient><linearGradient x1="620.414" y1="354.188" x2="659.586" y2="461.812" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill6"><stop offset="0" stop-color="#7F7F7F"/><stop offset="0.18" stop-color="#7F7F7F"/><stop offset="0.91" stop-color="#404040"/><stop offset="1" stop-color="#404040"/></linearGradient><clipPath id="clip7"><rect x="-2" y="-2" width="62" height="67"/></clipPath><clipPath id="clip8"><rect x="0" y="0" width="60" height="64"/></clipPath><linearGradient x1="544.51" y1="325.968" x2="587.49" y2="362.032" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill9"><stop offset="0" stop-color="#775CFE"/><stop offset="0.18" stop-color="#775CFE"/><stop offset="0.83" stop-color="#2473DC"/><stop offset="1" stop-color="#2473DC"/></linearGradient><clipPath id="clip10"><rect x="-2" y="-2" width="77" height="75"/></clipPath><clipPath id="clip11"><rect x="0" y="0" width="75" height="70"/></clipPath><linearGradient x1="540.733" y1="275.868" x2="586.267" y2="330.133" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill12"><stop offset="0" stop-color="#C45DD5"/><stop offset="0.18" stop-color="#C45DD5"/><stop offset="0.69" stop-color="#A266E4"/><stop offset="1" stop-color="#A266E4"/></linearGradient><clipPath id="clip13"><rect x="-2" y="-2" width="72" height="67"/></clipPath><clipPath id="clip14"><rect x="0" y="0" width="69" height="65"/></clipPath><linearGradient x1="577.565" y1="332.949" x2="631.435" y2="364.051" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill15"><stop offset="0" stop-color="#61A2F9"/><stop offset="0.18" stop-color="#61A2F9"/><stop offset="0.91" stop-color="#3159D7"/><stop offset="1" stop-color="#3159D7"/></linearGradient><clipPath id="clip16"><rect x="-2" y="-2" width="82" height="85"/></clipPath><clipPath id="clip17"><rect x="0" y="0" width="79" height="83"/></clipPath><linearGradient x1="586.612" y1="270.648" x2="638.388" y2="332.352" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill18"><stop offset="0" stop-color="#8A66FE"/><stop offset="0.19" stop-color="#8A66FE"/><stop offset="1" stop-color="#2473DC"/></linearGradient><linearGradient x1="571.897" y1="394.854" x2="581.103" y2="420.146" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill19"><stop offset="0" stop-color="#A6FF2F"/><stop offset="0.18" stop-color="#A6FF2F"/><stop offset="0.91" stop-color="#00D647"/><stop offset="1" stop-color="#00D647"/></linearGradient><image width="21" height="21" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAMAAACeyVWkAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEjUExURQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOijBK8AAABhdFJOUwABAgMEBQYICQoLDA0ODxAREhMUFRYXGBkaGxweHyAiJCUnKSotLi8wMzQ1Nz4/QkNERUZHSElKTE1PUlNVVldYWVpdXmFiY2VrbG1wcXN0dXd4eXp8fn+AhIaMjo+RlJVW9ugwAAAACXBIWXMAAA7DAAAOwwHHb6hkAAABXUlEQVQoU1WQaXfBYBCFR+IVQcSSlEjahBCCWFtbqX1XlNZWtP//V/QNznF6v80zZ+69Z+Cqx9ft/rSZvwRvsylv/ft42KwXi+V7jr0xiE/O+9V02G212r1RLXyF4s9xN+s3ynnDyOQrzcHFxTs5fo07JUPXVDWqpTLVLINp/bwbvxV0VRJ4jhee1HQxgdN/97NOIamEfG6n08X6xUjqmYX6cdUv6QrHUIgkrTaKCSjpCGwP04ahhhg7IgmCIBHtEWNJ2G2GZV3yUYiw4BALgWifrMFp3c1rghuRJsSYpNySBp+LlqHyTpK4QAACOQUF5ibl/tGgDOVlOxPlXdY7dQk8hD562Je13X1ZiQLIjSqpJ/+1gwV3cPj9eMvWmhlVZOhLXyvuy9nMo4dBNR0JeGgKIRvl8ITsJgRQssWUIt7+wLmuEIBJ5NIxWRKCgui7nN/kiSTjWljmcToAwB9ojy6IWqdEmwAAAABJRU5ErkJggg==" preserveAspectRatio="none" id="img20"></image><clipPath id="clip21"><rect x="566" y="397" width="21" height="21"/></clipPath></defs><g clip-path="url(#clip4)" transform="translate(-511 -252)"><path d="M551 382 581.093 339 700.907 339 731 382Z" fill="url(#fill5)" fill-rule="evenodd"/><path d="M544 390.667C544 385.88 547.88 382 552.667 382L727.333 382C732.12 382 736 385.88 736 390.667L736 425.333C736 430.12 732.12 434 727.333 434L552.667 434C547.88 434 544 430.12 544 425.333Z" fill="url(#fill6)" fill-rule="evenodd"/><g clip-path="url(#clip7)" filter="url(#fx0)" transform="translate(536 316)"><g clip-path="url(#clip8)"><path d="M49 53 17.3335 53C13.8356 53 11 50.1653 11 46.6686L11 12.3615C17.8235 11.5508 20.5867 10.677 26.92 11.1188 33.2533 11.5607 42.1629 13.4608 49 15.0126L49 53Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M585 365 553.333 365C549.836 365 547 362.165 547 358.669L547 324.361C553.823 323.551 556.587 322.677 562.92 323.119 569.253 323.561 578.163 325.461 585 327.013L585 365Z" fill="url(#fill9)" fill-rule="evenodd"/><g clip-path="url(#clip10)" filter="url(#fx1)" transform="translate(526 272)"><g clip-path="url(#clip11)"><path d="M10.9999 59.0001 10.9999 19.0002C10.9999 14.5818 14.5751 11.0001 18.9853 11.0001L59.6521 11.0001C62.1937 23.0649 64.1178 27.1297 63.9943 35.1297 63.8708 43.1297 60.6054 51.0433 58.911 59.0001 47.3722 57.4593 44.1286 56.0488 35.8334 55.9184 27.4934 55.7874 19.2778 57.9729 10.9999 59.0001Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M537 327 537 287C537 282.582 540.575 279 544.985 279L585.652 279C588.194 291.065 590.118 295.13 589.994 303.13 589.871 311.13 586.605 319.043 584.911 327 573.372 325.459 570.129 324.049 561.833 323.918 553.493 323.787 545.278 325.973 537 327Z" fill="url(#fill12)" fill-rule="evenodd"/><g clip-path="url(#clip13)" filter="url(#fx2)" transform="translate(570 320)"><g clip-path="url(#clip14)"><path d="M58 11 58 46.8332C58 50.7913 54.8044 54 50.8625 54L15.1758 54C13.0885 43.4096 11.0632 42.5869 11.0011 32.8194 10.936 22.5735 13.7842 18.2731 15.1758 11L58 11Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M628 327 628 362.833C628 366.791 624.804 370 620.862 370L585.176 370C583.088 359.41 581.063 358.587 581.001 348.819 580.936 338.573 583.784 334.273 585.176 327L628 327Z" fill="url(#fill15)" fill-rule="evenodd"/><g clip-path="url(#clip16)" filter="url(#fx3)" transform="translate(573 264)"><g clip-path="url(#clip17)"><path d="M11.175 11.115 58.3831 11.115C63.5977 11.115 67.825 15.3407 67.825 20.5535L67.825 67.7447C53.4389 69.7049 48.4347 72.7265 39.0529 71.6651L11.175 67.7447C12.8462 54.759 14.4843 51.2115 14.5175 41.7732 14.5534 31.5538 12.2892 21.3344 11.175 11.115Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M585 272 630.833 272C635.896 272 640 276.103 640 281.164L640 326.98C626.033 328.883 621.174 331.817 612.066 330.787L585 326.98C586.623 314.373 588.213 310.929 588.245 301.765 588.28 291.844 586.082 281.922 585 272Z" fill="url(#fill18)" fill-rule="evenodd"/><path d="M566 407.5C566 401.701 570.701 397 576.5 397 582.299 397 587 401.701 587 407.5 587 413.299 582.299 418 576.5 418 570.701 418 566 413.299 566 407.5Z" fill="url(#fill19)" fill-rule="evenodd"/><g clip-path="url(#clip21)"><use width="100%" height="100%" xlink:href="#img20" transform="translate(566 397)"></use></g></g></svg>