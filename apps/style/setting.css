@font-face {
    font-family: SettingsIcons;
    src: url(../icons/setting/icons.woff2) format("woff2")
}

#win-setting>.page>.cnt>.title {
    font-size: 29px;
    padding: 10px 5px 10px 5px;
    display: block;
    margin: 0;
}

.s-icon {
    font-family: SettingsIcons;
}

#win-setting {
    display: flex;
}

#win-setting>* {
    height: 100%
}

#win-setting>.page {
    flex-grow: 1
}


#win-setting>.menu {
    width: 30% !important;
    overflow: hidden;
    min-width: 280px;
    /* max-width: 30%; */
    padding: 3px 5px 5px 15px;
}

#win-setting>.menu>.user {
    display: flex;
    padding: 10px 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    /* transition: 200ms; */
    align-items: center;
}

#win-setting>.menu>.user:hover {
    background-color: var(--hover);
}

#win-setting>.menu>.user>div>p:first-child {
    font-size: 19px;
    margin-bottom: -7px;
}

#win-setting>.menu>.user>div>p:last-child {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
}

#win-setting>.menu>.user>div {
    padding: 0px 0 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#win-setting>.menu>.user>svg {
    background-color: var(--hover-b);
    /* box-shadow: 0 0 10px var(--sd); */
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    padding: 11px;
}

#win-setting>.menu>list {
    padding-bottom: 50px !important;
    margin-left: 5px;
    margin-top: 50px;
    height: calc(100% - 138px);
    overflow-y: auto;
    padding-right: 10px;
    border-radius: 6px;
}

#win-setting>.menu>list>a {
    padding: 5px 10px 5px 20px;
    font-size: 15px;
    display: flex;
    margin-bottom: 4px;
    border-radius: 6px;
}

#win-setting>.menu>list>a:not(.enable) {
    transform: none !important;
    filter: contrast(0.6) brightness(0.8) opacity(0.3);
}

#win-setting>.menu>list>a.enable:not(.avlb) {
    filter: contrast(0.7)  opacity(0.7) !important;
}

#win-setting>.menu>list>a>p {
    margin: 5px 7px;
}

#win-setting>.menu>list>a>img {
    height: 20px;
    width: 20px;
    margin: 1px 10px 0 0;
}

#win-setting>.menu>list>a.check {
    background-color: var(--hover) !important;
}

#win-setting>.page {
    overflow: hidden;
    padding-left: 15px;
    padding-right: 5px;
    margin: 2px 0 0 0;
}

#win-setting>.page>.cnt {
    overflow-y: scroll;
    opacity: 0;
    height: 0;
    transition: transform 300ms cubic-bezier(0, 0, 0, 1), opacity 300ms 50ms, height 0ms;
    transform: translate(0, 50%);
}

#win-setting>.page>.cnt.show {
    height: 100%;
    transform: none;
    opacity: 1;
    display: block;
}

.setting-list {
    display: flex;
    flex-direction: column;
    padding: 0 5px 60px 5px;
    margin-bottom: 0;
    border-radius: 10px;
    width: 100%;
}

.setting-list>* {
    width: 100%;
    color: var(--text);
    background: var(--card);
    margin-bottom: 7px;
    border-radius: 8px;
    /* border: 1.5px solid #6f6f6f30; */
    box-shadow: 0 1px 2px 0px var(--s3d);
    text-decoration: none;
    display: flex;
    padding: 10px 15px;
    justify-content: center;
    /* box-shadow: 0 1px 2px 1.5px var(--s3d); */
    transition: 100ms;
    justify-content: space-around;
    border: 2px solid #00000000;
    background-clip: padding-box;
    align-items: center; 
}

.setting-list>a:hover {
    text-decoration: none;
    background-color: var(--hover);
    border-color: var(--hover);
}

.setting-list>a:active {
    filter: opacity(0.75);
}

.setting-list>a.dp {
    margin-bottom: 1px;
    z-index: 1;
}

.setting-list>a.dp.show {
    /* border-bottom: none; */
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-bottom: 1px;
    box-shadow: 0 0.5px 1px 1px var(--s3d);
}

.setting-list>a.dp>.bi::before {
    transition: 150ms;
}

.setting-list>a.dp:active>.bi::before {
    transform: scale(0.7);
}

.setting-list>a.dp.show:active>.bi::before {
    transform: scale(0.7) rotate(-180deg);
}

.setting-list>a.dp.show>.bi::before {
    transform: rotate(-180deg);
}

.setting-list>div.dp {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    max-height: 0;
    transition: max-height 300ms, padding 300ms 100ms, opacity 200ms;
    overflow: hidden;
    display: block;
    padding: 0 15px;
    opacity: 0;
    margin:0 0 6px 0;
    box-shadow: none;
}

.setting-list>div.dp.show {
    padding: 10px 15px;
    transition: max-height 300ms, padding 200ms 50ms, opacity 200ms 100ms;
    max-height: 1000px;
    opacity: 1;
    box-shadow: 0 1px 2px 1px var(--s3d);
}

.checkbox {
    width: 40px;
    height: 20px;
    border-radius: 10px;
    background: linear-gradient(90deg, var(--hover-b), var(--hover-b));
    transition: 200ms;
}

.checkbox::before {
    content: '';
    display: block;
    height: 16px;
    width: 16px;
    border-radius: 8px;
    background: #fff;
    position: relative;
    top: 2px;
    left: 2px;
    transition: 100ms;
    transform: scale(0.8);
}

.checkbox.checked {
    background: linear-gradient(90deg, var(--theme-1), var(--theme-2));
}

.checkbox.checked::before {
    left: 22px;
}

.checkbox:hover::before {
    transform: none;
}

.checkbox:active::before {
    width: 20px;
}

.checkbox.checked:active::before {
    left: 18px;
}

div.app-color .color {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    margin: 3px 0 0 5px;
    outline: none;
    border: none;
    padding: 0;
    transition: 200ms;
}

div.app-color .act.color:active {
    transform: scale(0.8);
}

div.app-color input[type=color] {
    width: 0;
    height: 0;
    overflow: hidden;
    border: none;
    padding: 0;
    outline: none;
    margin: 0;
}

.setting-list>*>icon {
    font-family: SettingsIcons;
    font-size: 20px;
    margin: 10px 20px 10px 5px;
    background-image: linear-gradient(100deg, var(--theme-1), var(--theme-2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: saturate(130%) brightness(1.1);
}

.setting-list>*>icon.b {
    font-family: bootstrap-icons;
}

.setting-list>*>div>p:first-child {
    font-size: 19px;
    margin:  3px 0 0 0;
}

.setting-list>*>div>p:last-child {
    font-size: 10px;
    color: #7f7f7f;
    margin-bottom: 0px;
}

.setting-list>*>.bi,
.setting-list>*>.alr {
    flex-grow: 1;
    margin-top: 15px;
    color: #7f7f7f;
    font-size: 20px;
}

.setting-list>*>.alr>* {
    float: right;
    margin-right: 20px;
}

.setting-list>*>.bi::before {
    position: relative;
    float: right;
    right: 5px;
}

#set-theme {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
}

#set-theme>.a {
    width: 100px;
    height: 100px;
    border-radius: 10px;
}

#set-theme>.a {
    background-size: cover;
    color: #fff;
    text-shadow: 1px 1px 3px #000;
    text-align: right;
    padding: 3px 8px;
    box-shadow: 1px 1px 5px var(--sd);
    margin: 7px 7px 0 0;
}

#set-theme>.a:hover {
    filter: brightness(1.3) saturate(1.3);
}

#set-theme>.a:active {
    opacity: 0.75;
}

#win-setting>.page>.cnt.update>.lo>#svg5{
    width: 80px;
    margin: 15px 20px 30px 20px;
}

#win-setting>.page>.cnt.update>.lo{
    display: flex;
    align-items: center;
}

#win-setting>.page>.cnt.update>.lo div{
    flex-grow: 1;
    font-size: 20px;
    padding: 20px 5px;
}
#win-setting>.page>.cnt.update>.lo div>.detail{
    flex-grow: 1;
    font-size: 14px;
    color: #7f7f7f;
}

#win-setting>.page>.cnt.update>.lo>.update-main {
    display: flex;
    justify-content: center;
}

#win-setting>.page>.cnt.update>.lo>.update-main>div {
    display: flex;
    align-items: center;
}

#win-setting>.page>.cnt.update>.lo>.update-main>div:first-child {
    flex-direction: column;
    align-items: flex-start;
}

#win-setting>.page>.cnt.update>.lo>.update-main>div:last-child {
    justify-content: flex-end;
}

#win-setting>.page>.cnt.update .disabled {
    opacity: 0.5;
    pointer-events: none;
}
.blueteeth-tips-text{
	font-size:8px;
}
.setting-user-svg{
	    width: 100px;
	    height: 100px;
	    background-color: var(--hover-b);
	    /* box-shadow: 0 0 10px var(--sd); */
	    width: 60px;
	    min-width: 60px;
	    height: 60px;
	    border-radius: 50%;
	    padding: 11px;
	    margin-right: 15px;
	    margin-bottom: 15px;
}
/* 时间 必须样式 @Junchen Yi 2023-9-16 */
.selectLanguageA{
    min-height: 67px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
	filter: opacity(1);
}

.languageSelect {
    width: 130px;
        height: 30px;
        text-align: left;
        border-radius: 12px;
        background: var(--card);
        color: var(--text);
		position: absolute;
		    right: 40px;
			margin-right: -20px;
}
.languageSelect option {
    /* 添加样式，例如设置字体颜色和背景颜色 */
	color: var(--text);
    background-color: #fff;
    padding: 5px;
}
.settingTimeItem{
    margin-left: 30px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: flex-start;
    align-items: center;
}
.settingTimeTitle{
	
}
.settingTime{
	font-size: 28px;
}
/* 时间 必须样式 end @Junchen Yi 2023-9-16 */
