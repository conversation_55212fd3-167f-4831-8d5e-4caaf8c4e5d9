#win-about {
    display: flex;
}

#win-about>.menu {
    height: 100%;
    width: 20%;
    min-width: 180px;
    max-width: 250px;
    padding: 8px 5px 0 14px;
}

#win-about>.menu>list>* {
    padding: 6px 8px 5px 12px;
    display: flex;
    margin-bottom: 3px;
    border-radius: 7px;
}

#win-about>.menu>list>*>img {
    margin: 2px 7px 0 0;
}

#win-about>.cnt {
    overflow: auto;
    transform: translate(0, 50%);
    opacity: 0;
    width: 0;
    transition: transform 300ms cubic-bezier(0, 0, 0, 1), opacity 300ms 50ms;
    padding: 0 5px 60px 0;
    /* display: none; */
}

#win-about>.cnt.show {
    flex-grow: 1;
    transform: none;
    opacity: 1;
    display: block;
}

#win-about>.cnt .tit {
    margin: 8px 0 3px 0;
    display: flex;
}

#win-about>.cnt .tit>span {
    background: linear-gradient(180deg, var(--theme-1), var(--theme-2));
    border-radius: 10px;
    width: 6px;
    margin: 5px 8px 5px 5px;
    transition: 200ms;
    /* opacity: 0; */
    transform: scaleY(0);
    transform-origin: top;
}

#win-about>.cnt .tit:hover>span {
    transform: none;
}

#win-about>.update>div>details {
    background-color: var(--card);
    padding: 10px 10px 0 15px;
    border-radius: 8px;
    /* box-shadow: 3px 3px 5px var(--sd); */
    box-shadow: 0 1px 2px 1px var(--s3d);
    margin: 12px 10px 0 0;
    transition: 100ms, height 0ms, padding-bottom 100ms ease-out;
    overflow: hidden;
    height: 45px;
}

#win-about>.update>div>details[open] {
    padding-bottom: 10px;
    height: 100%;
}

#win-about>.update>div>details:not([open]):hover {
    filter: brightness(1.2);
}

#win-about>.update>div>details:active {
    opacity: 0.6;
}

#win-about>.update>div>details>summary {
    font-size: 17px;
    transition: 200ms;
}

#win-about>.update>div>details>summary>span {
    font-weight: 900;
}

#win-about>.update>div>details>p {
    margin-left: 10px;
}

#contri {
    display: flex;
    flex-wrap: wrap;
}

#contri>.a {
    margin: 3px;
    background: var(--card);
    box-shadow: 0 1px 2px var(--sd);
    border-radius: 10px;
    width: 110px;
    height: 70px;
    text-align: center;
    padding-top: 10px;
    font-size: 13px;
    transition: 50ms;
}

#contri>.a:hover {
    background: var(--hover);
}

#contri>.a:active {
    opacity: 0.6;
}

#contri>.a>.name {
    font-size: 17px;
    margin: 0px 6px;
    /* border: 6px; */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

#contri>.a>.cbs>.num {
    font-size: 15px;
    font-weight: 550;
}

#contri>.button {
    height: max-content;
    margin: 10px 0 0 10px;
}

.window.winver{
    width: 600px;
    height: 550px;
}

#win-winver>.logo{
    display: flex;
    justify-content: center;
    align-items: center;
}

#win-winver>.logo>.img{
    background: url(../../icon/logo.svg) center;
    background-size: cover;
    height: 70px;
    width: 70px;
    margin:10px;
}

#win-winver>.logo>p{
    background-image: linear-gradient(100deg, #b660d9,#2983cc);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 45px;
    /* font-weight: 450; */
    font-family:
    'Microsoft Yahei UI';
    margin: 15px;
}

#win-winver>hr{
    width: calc(100% - 20px);
    margin: 10px;
}

#win-winver p{
    margin: 0 50px;
}

#win-winver>.mesg:hover{
    text-decoration: underline!important;
}

#win-winver>.mesg:active{
    opacity: 0.5;
    transition: 30ms;
}

#win-winver>.bottom{
    position: absolute;
    bottom: 80px;
}
