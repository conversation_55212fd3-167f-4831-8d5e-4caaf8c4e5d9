.rainbow-container-main {
    font-size: 11vmin;
    display: flex;
  }
  .draggable {
    width: 100px;
    height: 30px;
    position: absolute;
    cursor: move;
    z-index: 98;
}
.no-drag {
  cursor: default;
}
  .rainbow-container {
    z-index: 1;
    transform: rotate(0deg) translateZ(0);
    transform-origin: center center;
    width: 1em;
    height: 1em;
    border: 1px solid rgba(255, 255, 255, 0.4);
    -webkit-animation: rainbow 3s infinite linear;
    border-radius: 0.5em;
    box-shadow: 0 0 0.3em 0.05em #2c116e, inset 0.03em 0 0.1em 0.02em #de66e4;
    transform-style: preserve-3d;
    perspective: 1em;
  }
  
  @keyframes rainbow {
    0% {
      transform: rotate(0deg) translateZ(0);
      box-shadow: 0 0 0.3em 0.05em #2c116e, inset 0.03em 0 0.1em 0.02em #de66e4;
    }
    25% {
      transform: rotate(90deg) translateZ(0);
      box-shadow: 0 0 0.3em 0.05em #28126a, inset 0.03em 0 0.1em 0.02em #34ceaa;
    }
    50% {
      transform: rotate(180deg) translateZ(0);
      box-shadow: 0 0 0.3em 0.05em #28126a, inset 0.03em 0 0.1em 0.02em #19b3f5;
    }
    75% {
      transform: rotate(270deg) translateZ(0);
      box-shadow: 0 0 0.3em 0.05em #28126a, inset 0.03em 0 0.1em 0.02em #3d52ac;
    }
    100% {
      transform: rotate(360deg) translateZ(0);
      box-shadow: 0 0 0.3em 0.05em #28126a, inset 0.03em 0 0.1em 0.02em #de66e4;
    }
  }
  .rainbow-container {
    position: relative;
  }
  .rainbow-container > div {
    position: absolute;
    top: 0.1em;
    left: 0.1em;
    width: 0.8em;
    height: 0.8em;
    border-radius: 50%;
  }
  
  .rainbow-container .green {
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 60%, rgba(115, 213, 186, 0.8) 100%);
    transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 0);
    animation: curve-rotate-green 6s infinite linear;
  }
  
  .rainbow-container .pink {
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 50%, rgba(215, 115, 229, 0.8) 100%);
    transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 0);
    animation: curve-rotate-pink 3s infinite linear;
  }
  
  @-webkit-keyframes curve-rotate-green {
    0% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @-moz-keyframes curve-rotate-green {
    0% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @-o-keyframes curve-rotate-green {
    0% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @keyframes curve-rotate-green {
    0% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(0) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @-webkit-keyframes curve-rotate-pink {
    0% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @-moz-keyframes curve-rotate-pink {
    0% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @-o-keyframes curve-rotate-pink {
    0% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  @keyframes curve-rotate-pink {
    0% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 0deg);
    }
    50% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 180deg);
    }
    100% {
      transform: rotateY(180deg) skew(14deg) rotate3d(1.1, 1, 0.9, 360deg);
    }
  }
  .footer {
    position: fixed;
    bottom: 1em;
    right: 1em;
    font-size: 12px;
    font-style: italic;
    color: white;
  }
  .footer a {
    color: white;
  }
  