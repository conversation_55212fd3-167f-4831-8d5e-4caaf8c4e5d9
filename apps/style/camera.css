.camera-notice {
    width: 300px !important;
    height: 400px !important;
}

#win-camera {
    position: relative;
}

#win-camera>.video {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

#win-camera>.control>div {
    width: 45px;
    height: 45px;
}

#win-camera>.control>div>.startbutton {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: #ffffffee;
    backdrop-filter: blur(10px) saturate(1.3);
    box-shadow: 0 0 0 4px #44444470, 0 0 0 7px #ffffffa0, 1px 1px 15px 7px #00000090;
    transition: 200ms;
}

#win-camera>.control>div>.startbutton:hover {
    background-color: #ffffffc0;
}

#win-camera>.control {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #00000010;
}

#win-camera.h>.control {
    height: 100%;
    width: 80px;
    right: 0px;
    top: 0px;
}

#win-camera.v>.control {
    height: 80px;
    width: 100%;
    bottom: 0px;
    left: 0px;
}

#win-camera.h video {
    height: 100%;
}

#win-camera.v video {
    width: 100%;
}