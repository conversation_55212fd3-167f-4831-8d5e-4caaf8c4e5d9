@font-face {
    font-family: StoreIcons;
    src: url("../icons/msstore/icons.ttf");
}
/* I love CSS! */
#win-msstore {
    display: grid;
    grid-template-columns: 80px auto;

    & icon {
        font-family: StoreIcons;
    }

    &>.menu {
        & .m-icon {
            color: transparent;
            -webkit-text-stroke: 2px #000;
        }

        &>list {
            margin-left: 5px;
            overflow-y: auto;
            padding-right: 10px;
            border-radius: 6px;
            height: 100%;

            & icon {
                font-size: 1.8em;
                margin-bottom: 0px;
                line-height: 1.1;
                transition: all 500ms;
            }

            & .t2 {
                font-size: 0.7em !important;
                color: var(--text2);
                transition: all 500ms;
                overflow: hidden;
                opacity: 1.0;
            }

            &>a {
                padding: 5px 10px;
                height: 59px !important;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-bottom: 4px;
                border-radius: 6px;
                transition: all 500ms;
                overflow: hidden;
                position: relative;

                &.check {
                    background-color: var(--hover) !important;

                    & icon {
                        font-size: 2.3em;
                        background-image: linear-gradient(to right bottom, var(--theme-1), var(--theme-2));
                        -webkit-background-clip: text;
                        background-clip: text;
                        color: #00000022;
                    }

                    & .t2 {
                        opacity: 0.0;
                        margin-bottom: -20px;
                    }
                }
            }

            &>span.focs {
                height: 26px;
                margin-top: -47px;
            }
        }
    }

    &>.page {
        background-color: var(--bg50);
        border-radius: 10px 0px 0px 0px;

        &>.cnt {
            transition: transform 300ms cubic-bezier(0, 0, 0, 1), opacity 300ms 50ms, height 0ms, padding 0ms;
            transform: translate(0, 50%);
            opacity: 0;
            height: 0px;
            padding: 0px;
            overflow: hidden;

            &.show {
                height: 100%;
                transform: none !important;
                opacity: 1;
                overflow-y: auto;
                min-height: 0px;
            }

            &.home {
                &>.container {
                    height: 100%;
                    display: grid;
                    grid-template-rows: auto min-content;
                    padding: 0px 15px 30px 15px;

                    &>.apps {
                        &>.tit {
                            margin-left: 30px;
                            font-size: 1.6em;
                            line-height: 1.5;
                        }

                        &>.app-cards {
                            display: grid;
                            grid-template-columns: auto auto auto;
                            grid-template-rows: auto auto auto;

                            &>* {
                                background-color: var(--hover);
                                margin: 10px;
                                border-radius: 10px;
                                padding: 14px;
                                display: flex; 

                                &>.left {
                                    width: 70px;
                                    height: 70px;
                                    background: initial;
                                }

                                &>.right {
                                    width: 100%;

                                    &>.tit {
                                        width: 100%;

                                        &>.up {
                                            width: 100%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;

                                            &>.type {
                                                font-size: 0.85em;
                                                padding: 4px 8px;
                                                border-radius: 5px;
                                                background-color: var(--fill);
                                                color: var(--text2);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.m-icon {
    font-family: StoreIcons;
}