#win-notepad{
    overflow: hidden;
}

#win-notepad>.tool{
    width: 100%;
    height: 35px;
    padding-top: 5px;
    margin-bottom: 2px;
}

#win-notepad>.text-box{
    border-radius: 10px 10px 0px 0px;
    /* width: 100%; */
    border: none;
    background-color: var(--card);
    opacity: 0.8;
    transform: translate(0,8px);
    outline: none;
    box-shadow: 0 -3px 5px var(--s3d);
    padding: 10px;
    resize: none;
    font-size: 15px;
    line-height: 1.25;
    color: var(--text);
    height: calc(100% - 35px);
    transition: 200ms;
    margin: 0 10px;
}

#win-notepad>.text-box *:not(div):not(br){
    border-radius: 8px;
    outline: 2px solid transparent;
    transition: 100ms;
    display: block;
}

#win-notepad>.text-box *:not(div):not(br):focus{
    background: red;
}

#win-notepad>.text-box *:not(div):not(br):hover{
    outline-color: #2983cc;
}

#win-notepad>.text-box:focus{
    transform: none;
    opacity: 1;
    box-shadow: 0 -5px 7px var(--s3d);
}

#win-notepad>.text-box::selection{
    background: #2983cc;
    color: #ddd;
}

#win-notepad>.text-box.down{
    transform: translate(0,100%);
}

#win-notepad>.tool>a{
    border-radius: 10px;
    margin: -5px 0 0 5px;
    text-align: center;
    padding: 3px 5px;
    display: inline-block;
    transition: 100ms;
}

#win-notepad>.tool>a:hover{
    background-color: var(--hover);
}

#win-notepad-font>.row {
    display: flex;
    justify-content: space-around;
    height: 266px;
}

.select-input {
    display: inline-block;
    height: 200px;
}

.select-input#win-notepad-font-type {
    width: 200px;
}

.select-input#win-notepad-font-size {
    width: 80px;
}

.select-input#win-notepad-font-style {
    width: 90px;
}

.select-input#win-notepad-font-style>.value-box{
    padding: 5px;
}

/* #win-notepad-font-size {
    vertical-align: top;
} */


.select-input>input[type=text] {
    outline: none;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 10px 10px 0px 0px;
    border: 2px solid #7f7f7f40;
    background: var(--card);
    border-bottom: none;
    color: var(--text);
    padding: 3px 6px;
    /* box-shadow: 2px 2px 5px var(--sd); */
}

.select-input>.description {
    margin-left: 3px;
}

.select-input>.value-box {
    width: 100%;
    height: 100%;
    border: 2px solid #7f7f7f40;
    background: var(--card);
    border-radius: 0px 0px 10px 10px;
    border-style: solid;
    overflow-y: auto;
    overflow-x: hidden;
    /* background-color: rgba(255, 255, 255, 0.3); */
    /* box-shadow: inset 0px 4px 2.5px var(--sd); */
    display: block;
    padding: 5px 0 5px 5px;
    /* box-shadow: 2px 2px 5px var(--sd); */

}

.select-input>.value-box>.option {
    padding: 3px 7px;
}

/* .select-input>.value-box>.option {
    padding: 3px;
    width: 100%;
} */

#win-notepad-font .preview {
    display: inline-block;
    width: 150px;
    height: 150px;
    margin: 5px 10px;;
}

#win-notepad-font .preview>.preview-box {
    display: flex;
    width: 150px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    word-break: keep-all;
    height: 150px;
    text-align: center;
    border: 2px solid #7f7f7f40;
    background: var(--card);
    border-width: 2px;
    overflow: hidden;
    border-radius: 10px;
    line-height: 1.25;
}