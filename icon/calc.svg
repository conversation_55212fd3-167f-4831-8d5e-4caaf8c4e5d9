<svg  viewBox="0,0,292,354" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.51852 6.55493"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx4" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx5" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx6" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.51852"/></filter><filter id="fx7" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><filter id="fx8" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.48062 6.47925"/></filter><clipPath id="clip9"><rect x="496" y="180" width="292" height="354"/></clipPath><linearGradient x1="547.265" y1="195.878" x2="732.735" y2="517.122" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill10"><stop offset="0" stop-color="#949494"/><stop offset="0.24" stop-color="#949494"/><stop offset="0.74" stop-color="#7F7F7F"/><stop offset="1" stop-color="#7F7F7F"/></linearGradient><clipPath id="clip11"><rect x="-6.33333" y="-3.35196" width="107.667" height="68.7151"/></clipPath><clipPath id="clip12"><rect x="0" y="0" width="98" height="60"/></clipPath><linearGradient x1="605.476" y1="207.703" x2="674.524" y2="327.297" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill13"><stop offset="0" stop-color="#75C2FB"/><stop offset="0.18" stop-color="#75C2FB"/><stop offset="0.91" stop-color="#4E92EC"/><stop offset="1" stop-color="#4E92EC"/></linearGradient><clipPath id="clip14"><rect x="-6.62791" y="-6.6265" width="68.2674" height="68.253"/></clipPath><clipPath id="clip15"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="623.541" y1="421.492" x2="656.459" y2="478.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill16"><stop offset="0" stop-color="#D5D5D5"/><stop offset="0.18" stop-color="#D5D5D5"/><stop offset="0.91" stop-color="#C2C2C2"/><stop offset="1" stop-color="#C2C2C2"/></linearGradient><clipPath id="clip17"><rect x="-6.62791" y="-6.62651" width="68.2674" height="68.253"/></clipPath><clipPath id="clip18"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="623.541" y1="362.492" x2="656.459" y2="419.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill19"><stop offset="0" stop-color="#D9D9D9"/><stop offset="0.18" stop-color="#D9D9D9"/><stop offset="0.91" stop-color="#BFBFBF"/><stop offset="1" stop-color="#BFBFBF"/></linearGradient><clipPath id="clip20"><rect x="-6.62791" y="-6.62651" width="68.2674" height="68.253"/></clipPath><clipPath id="clip21"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="684.541" y1="303.492" x2="717.459" y2="360.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill22"><stop offset="0" stop-color="#D9D9D9"/><stop offset="0.18" stop-color="#D9D9D9"/><stop offset="0.91" stop-color="#BFBFBF"/><stop offset="1" stop-color="#BFBFBF"/></linearGradient><clipPath id="clip23"><rect x="-6.62791" y="-6.6265" width="68.2674" height="68.253"/></clipPath><clipPath id="clip24"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="562.541" y1="421.492" x2="595.459" y2="478.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill25"><stop offset="0" stop-color="#D9D9D9"/><stop offset="0.18" stop-color="#D9D9D9"/><stop offset="0.91" stop-color="#BFBFBF"/><stop offset="1" stop-color="#BFBFBF"/></linearGradient><clipPath id="clip26"><rect x="-6.62791" y="-6.62651" width="68.2674" height="68.253"/></clipPath><clipPath id="clip27"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="562.541" y1="362.492" x2="595.459" y2="419.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill28"><stop offset="0" stop-color="#E0E0E0"/><stop offset="0.18" stop-color="#E0E0E0"/><stop offset="0.91" stop-color="#C4C4C4"/><stop offset="1" stop-color="#C4C4C4"/></linearGradient><clipPath id="clip29"><rect x="-6.6279" y="-6.33334" width="68.2674" height="85.6667"/></clipPath><clipPath id="clip30"><rect x="0" y="0" width="57" height="75"/></clipPath><linearGradient x1="681.085" y1="365.785" x2="720.915" y2="475.215" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill31"><stop offset="0" stop-color="#7B5DFD"/><stop offset="0.18" stop-color="#7B5DFD"/><stop offset="0.83" stop-color="#4F5AE3"/><stop offset="1" stop-color="#4F5AE3"/></linearGradient><clipPath id="clip32"><rect x="-6.62791" y="-6.62651" width="68.2674" height="68.253"/></clipPath><clipPath id="clip33"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="623.541" y1="303.492" x2="656.459" y2="360.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill34"><stop offset="0" stop-color="#E0E0E0"/><stop offset="0.18" stop-color="#E0E0E0"/><stop offset="0.91" stop-color="#C4C4C4"/><stop offset="1" stop-color="#C4C4C4"/></linearGradient><clipPath id="clip35"><rect x="-6.62791" y="-6.62651" width="68.2674" height="68.253"/></clipPath><clipPath id="clip36"><rect x="0" y="0" width="57" height="55"/></clipPath><linearGradient x1="562.541" y1="303.492" x2="595.459" y2="360.508" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill37"><stop offset="0" stop-color="#E0E0E0"/><stop offset="0.18" stop-color="#E0E0E0"/><stop offset="0.91" stop-color="#C4C4C4"/><stop offset="1" stop-color="#C4C4C4"/></linearGradient></defs><g clip-path="url(#clip9)" transform="translate(-496 -180)"><path d="M528 244.334C528 223.715 544.715 207 565.334 207L714.666 207C735.285 207 752 223.715 752 244.334L752 468.666C752 489.285 735.285 506 714.666 506L565.334 506C544.715 506 528 489.285 528 468.666Z" fill="url(#fill10)" fill-rule="evenodd"/><g clip-path="url(#clip11)" filter="url(#fx0)" transform="matrix(3 0 0 2.98333 495 180)"><g clip-path="url(#clip12)"><path d="M19.9619 26.9505C19.9619 23.1524 23.0238 20.0734 26.8008 20.0734L71.123 20.0734C74.9 20.0734 77.9619 23.1524 77.9619 26.9505L77.9619 32.9728C77.9619 36.7709 74.9 39.8499 71.123 39.8499L26.8008 39.8499C23.0238 39.8499 19.9619 36.7709 19.9619 32.9728Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M553 258.517C553 247.186 562.186 238 573.517 238L706.483 238C717.814 238 727 247.186 727 258.517L727 276.483C727 287.814 717.814 297 706.483 297L573.517 297C562.186 297 553 287.814 553 276.483Z" fill="url(#fill13)" fill-rule="evenodd"/><g clip-path="url(#clip14)" filter="url(#fx1)" transform="matrix(3.01754 0 0 3.01818 556 369)"><g clip-path="url(#clip15)" transform="translate(-2.84217e-14 0)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M614 440.22C614 432.919 619.919 427 627.22 427L652.78 427C660.081 427 666 432.919 666 440.22L666 459.78C666 467.081 660.081 473 652.78 473L627.22 473C619.919 473 614 467.081 614 459.78Z" fill="url(#fill16)" fill-rule="evenodd"/><g clip-path="url(#clip17)" filter="url(#fx2)" transform="matrix(3.01754 0 0 3.01818 556 310)"><g clip-path="url(#clip18)" transform="translate(-2.84217e-14 1.42109e-14)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M614 381.22C614 373.919 619.919 368 627.22 368L652.78 368C660.081 368 666 373.919 666 381.22L666 400.78C666 408.081 660.081 414 652.78 414L627.22 414C619.919 414 614 408.081 614 400.78Z" fill="url(#fill19)" fill-rule="evenodd"/><g clip-path="url(#clip20)" filter="url(#fx3)" transform="matrix(3.01754 0 0 3.01818 617 251)"><g clip-path="url(#clip21)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M675 322.22C675 314.919 680.919 309 688.22 309L713.78 309C721.081 309 727 314.919 727 322.22L727 341.78C727 349.081 721.081 355 713.78 355L688.22 355C680.919 355 675 349.081 675 341.78Z" fill="url(#fill22)" fill-rule="evenodd"/><g clip-path="url(#clip23)" filter="url(#fx4)" transform="matrix(3.01754 0 0 3.01818 495 369)"><g clip-path="url(#clip24)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M553 440.22C553 432.919 558.919 427 566.22 427L591.78 427C599.081 427 605 432.919 605 440.22L605 459.78C605 467.081 599.081 473 591.78 473L566.22 473C558.919 473 553 467.081 553 459.78Z" fill="url(#fill25)" fill-rule="evenodd"/><g clip-path="url(#clip26)" filter="url(#fx5)" transform="matrix(3.01754 0 0 3.01818 495 310)"><g clip-path="url(#clip27)" transform="matrix(1 0 0 1 0 1.42109e-14)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M553 381.22C553 373.919 558.919 368 566.22 368L591.78 368C599.081 368 605 373.919 605 381.22L605 400.78C605 408.081 599.081 414 591.78 414L566.22 414C558.919 414 553 408.081 553 400.78Z" fill="url(#fill28)" fill-rule="evenodd"/><g clip-path="url(#clip29)" filter="url(#fx6)" transform="matrix(3.01754 0 0 3 617 310)"><g clip-path="url(#clip30)"><path d="M19.8458 24.9435C19.8458 22.1922 22.0632 19.9619 24.7985 19.9619L32.1257 19.9619C34.861 19.9619 37.0784 22.1922 37.0784 24.9435L37.0784 49.9803C37.0784 52.7315 34.861 54.9619 32.1257 54.9619L24.7985 54.9619C22.0632 54.9619 19.8458 52.7315 19.8458 49.9803Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M675 382.945C675 374.691 681.691 368 689.945 368L712.055 368C720.309 368 727 374.691 727 382.945L727 458.055C727 466.309 720.309 473 712.055 473L689.945 473C681.691 473 675 466.309 675 458.055Z" fill="url(#fill31)" fill-rule="evenodd"/><g clip-path="url(#clip32)" filter="url(#fx7)" transform="matrix(3.01754 0 0 3.01818 556 251)"><g clip-path="url(#clip33)" transform="translate(-2.84217e-14 0)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M614 322.22C614 314.919 619.919 309 627.22 309L652.78 309C660.081 309 666 314.919 666 322.22L666 341.78C666 349.081 660.081 355 652.78 355L627.22 355C619.919 355 614 349.081 614 341.78Z" fill="url(#fill34)" fill-rule="evenodd"/><g clip-path="url(#clip35)" filter="url(#fx8)" transform="matrix(3.01754 0 0 3.01818 495 251)"><g clip-path="url(#clip36)"><path d="M19.8458 24.2219C19.8458 21.8027 21.8073 19.8416 24.227 19.8416L32.6972 19.8416C35.1168 19.8416 37.0784 21.8027 37.0784 24.2219L37.0784 30.7023C37.0784 33.1215 35.1168 35.0826 32.6972 35.0826L24.227 35.0826C21.8073 35.0826 19.8458 33.1215 19.8458 30.7023Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M553 322.22C553 314.919 558.919 309 566.22 309L591.78 309C599.081 309 605 314.919 605 322.22L605 341.78C605 349.081 599.081 355 591.78 355L566.22 355C558.919 355 553 349.081 553 341.78Z" fill="url(#fill37)" fill-rule="evenodd"/></g></svg>