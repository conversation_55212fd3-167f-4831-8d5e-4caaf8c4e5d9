<svg viewBox="0,0,373,268" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.290196" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.22222 6.22222"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.290196" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.22222 6.22222"/></filter><linearGradient x1="493.018" y1="242.575" x2="664.982" y2="540.425" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill2"><stop offset="0" stop-color="#1065EE"/><stop offset="0.04" stop-color="#1065EE"/><stop offset="1" stop-color="#75C8FB"/></linearGradient><linearGradient x1="623.881" y1="454.84" x2="651.432" y2="369.762" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill3"><stop offset="0" stop-color="#AFABAB"/><stop offset="0.04" stop-color="#AFABAB"/><stop offset="1" stop-color="#FFFFFF"/></linearGradient></defs><g transform="translate(-422 -233)"><path d="M423 315.817C423 297.693 437.693 283 455.817 283L702.183 283C720.307 283 735 297.693 735 315.817L735 467.183C735 485.307 720.307 500 702.183 500L455.817 500C437.693 500 423 485.307 423 467.183Z" fill="url(#fill2)" fill-rule="evenodd"/><path d="M462.813 374.542C505.539 357.28 558.388 365.072 568.511 390.127 578.634 415.182 611.093 431.212 633.43 422.188" stroke="#FFFFFF" stroke-width="21" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><g filter="url(#fx0)" transform="translate(583 358)"><g><path d="M68.522 20.3483C71.0828 20.3145 73.6867 20.7439 76.2151 21.683 86.3278 25.4392 92.0366 36.1621 89.5067 46.6491 88.2418 51.8926 85.1169 56.2495 80.969 59.137L76.5365 61.1693 76.776 61.4348C67.9664 65.4393 64.6826 68.2424 55.1523 70.565L20.1893 76.4823C25.5167 68.6635 34.448 60.6043 39.7754 52.7855L49.8624 31.9454 49.8482 31.9186 49.9207 31.825 49.97 31.7232 49.9859 31.7408 54.6256 25.7429C58.4513 22.3373 63.4004 20.4159 68.522 20.3483Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M651.26 378.899C653.77 378.865 656.323 379.286 658.802 380.207 668.716 383.89 674.313 394.402 671.833 404.684 670.593 409.824 667.529 414.096 663.463 416.927L659.117 418.919 659.352 419.18C650.715 423.105 647.496 425.854 638.152 428.131L603.875 433.932C609.098 426.266 617.854 418.365 623.077 410.7L632.966 390.268 632.952 390.242 633.023 390.15 633.072 390.05 633.087 390.068 637.636 384.187C641.387 380.849 646.239 378.965 651.26 378.899Z" fill="url(#fill3)" fill-rule="evenodd"/><g filter="url(#fx1)" transform="translate(628 233)"><g><path d="M140.687 23.5685 140.687 23.5685C148.166 29.6179 149.324 40.5844 143.275 48.0629L48.489 165.241C46.2898 167.96 42.3031 168.381 39.5844 166.182L22.3477 152.239C19.6289 150.04 19.2078 146.053 21.4069 143.334L116.193 26.1561C122.242 18.6776 133.208 17.5191 140.687 23.5685Z" fill="#262626" fill-rule="evenodd"/></g></g><path d="M767.567 257.942 767.567 257.942C774.899 263.873 776.035 274.625 770.104 281.957L677.177 396.837C675.021 399.503 671.112 399.915 668.447 397.759L651.548 384.09C648.883 381.934 648.47 378.025 650.626 375.36L743.553 260.479C749.484 253.147 760.235 252.012 767.567 257.942Z" fill="#262626" fill-rule="evenodd"/></g></svg>