<svg viewBox="-17,-17,289,289" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.28148 6.22222"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.22222 6.28148"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.26972 6.28148"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.22222 6.27729"/></filter><clipPath id="clip4"><rect x="513" y="233" width="255" height="255"/></clipPath><linearGradient x1="553.257" y1="209.757" x2="726.743" y2="510.243" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill5"><stop offset="0" stop-color="#AD7EDC"/><stop offset="0.24" stop-color="#AD7EDC"/><stop offset="0.74" stop-color="#706AE4"/><stop offset="1" stop-color="#706AE4"/></linearGradient><clipPath id="clip6"><rect x="-3.53333" y="-3.5" width="61.0762" height="102"/></clipPath><clipPath id="clip7"><rect x="0" y="0" width="53" height="95"/></clipPath><clipPath id="clip8"><rect x="-3.5" y="-3.53333" width="85" height="61.0762"/></clipPath><clipPath id="clip9"><rect x="0" y="0" width="78" height="53"/></clipPath><clipPath id="clip10"><rect x="-3.5267" y="-3.53334" width="75.0687" height="61.0762"/></clipPath><clipPath id="clip11"><rect x="0" y="0" width="66" height="53"/></clipPath><clipPath id="clip12"><rect x="-3.5" y="-3.53097" width="64" height="64.0619"/></clipPath><clipPath id="clip13"><rect x="0" y="0" width="56" height="57"/></clipPath></defs><g clip-path="url(#clip4)" transform="translate(-513 -233)"><path d="M513 360C513 289.86 569.86 233 640 233 710.14 233 767 289.86 767 360 767 430.14 710.14 487 640 487 569.86 487 513 430.14 513 360Z" fill="url(#fill5)" fill-rule="evenodd"/><g clip-path="url(#clip6)" filter="url(#fx0)" transform="matrix(1.98113 0 0 2 591 289)"><g clip-path="url(#clip7)"><path d="M19.5494 26.335C19.5494 22.5325 22.6613 19.45 26.5 19.45L26.5 19.45C30.3387 19.45 33.4506 22.5325 33.4506 26.335L33.4506 68.665C33.4506 72.4675 30.3387 75.5501 26.5 75.5501L26.5 75.55C22.6613 75.55 19.5494 72.4675 19.5494 68.665Z" fill="#FFFFFF" fill-rule="evenodd"/></g></g><path d="M630 342.5C630 335.044 636.044 329 643.5 329L643.5 329C650.956 329 657 335.044 657 342.5L657 425.5C657 432.956 650.956 439 643.5 439L643.5 439C636.044 439 630 432.956 630 425.5Z" fill="#FFFFFF" fill-rule="evenodd"/><g clip-path="url(#clip8)" filter="url(#fx1)" transform="matrix(2 0 0 1.98113 566 373)"><g clip-path="url(#clip9)" transform="matrix(1 0 0 1 0 -2.84217e-14)"><path d="M19.62 26.5C19.62 22.6613 22.7025 19.5494 26.505 19.5494L51.495 19.5494C55.2975 19.5494 58.38 22.6613 58.38 26.5L58.38 26.5C58.38 30.3387 55.2975 33.4506 51.495 33.4506L26.505 33.4506C22.7025 33.4506 19.62 30.3387 19.62 26.5Z" fill="#FFFFFF" fill-rule="evenodd"/></g></g><path d="M606 425.5C606 418.044 612.044 412 619.5 412L668.5 412C675.956 412 682 418.044 682 425.5L682 425.5C682 432.956 675.956 439 668.5 439L619.5 439C612.044 439 606 432.956 606 425.5Z" fill="#FFFFFF" fill-rule="evenodd"/><g clip-path="url(#clip10)" filter="url(#fx2)" transform="matrix(1.98485 0 0 1.98113 566 290)"><g clip-path="url(#clip11)"><path d="M19.8957 26.5C19.8957 22.6613 23.0018 19.5494 26.8333 19.5494L39.1667 19.5494C42.9982 19.5494 46.1043 22.6613 46.1043 26.5L46.1043 26.5C46.1043 30.3387 42.9982 33.4506 39.1667 33.4506L26.8333 33.4506C23.0018 33.4506 19.8957 30.3387 19.8957 26.5Z" fill="#FFFFFF" fill-rule="evenodd"/></g></g><path d="M606 342.5C606 335.044 612.044 329 619.5 329L643.5 329C650.956 329 657 335.044 657 342.5L657 342.5C657 349.956 650.956 356 643.5 356L619.5 356C612.044 356 606 349.956 606 342.5Z" fill="#FFFFFF" fill-rule="evenodd"/><g clip-path="url(#clip12)" filter="url(#fx3)" transform="matrix(2 0 0 1.98246 584 241)"><g clip-path="url(#clip13)"><path d="M19.33 28.5C19.33 23.5272 23.2117 19.496 28 19.496 32.7883 19.496 36.67 23.5272 36.67 28.5 36.67 33.4728 32.7883 37.504 28 37.504 23.2117 37.504 19.33 33.4728 19.33 28.5Z" fill="#FFFFFF" fill-rule="evenodd"/></g></g><path d="M623 297.5C623 287.835 630.611 280 640 280 649.389 280 657 287.835 657 297.5 657 307.165 649.389 315 640 315 630.611 315 623 307.165 623 297.5Z" fill="#FFFFFF" fill-rule="evenodd"/></g></svg>