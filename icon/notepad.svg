<svg viewBox="0,0,316,322" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.11111 5.11111"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4.22222 4.23769"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.22222 6.22222"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.26136 6.22222"/></filter><filter id="fx4" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6.25116 6.22222"/></filter><filter id="fx5" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4.44444 4.44444"/></filter><filter id="fx6" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4.44444 4.44444"/></filter><filter id="fx7" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4.44444 4.44444"/></filter><filter id="fx8" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4.44444 4.44444"/></filter><clipPath id="clip9"><rect x="482" y="203" width="316" height="322"/></clipPath><linearGradient x1="508.792" y1="323.744" x2="771.208" y2="419.256" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill10"><stop offset="0" stop-color="#C45DD5"/><stop offset="0.18" stop-color="#C45DD5"/><stop offset="0.69" stop-color="#A266E4"/><stop offset="1" stop-color="#A266E4"/></linearGradient><clipPath id="clip11"><rect x="-5.5" y="-5.5" width="132" height="150"/></clipPath><clipPath id="clip12"><rect x="0" y="0" width="120" height="140"/></clipPath><linearGradient x1="540" y1="360.5" x2="740" y2="360.5" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill13"><stop offset="0" stop-color="#FFFFFF"/><stop offset="0.18" stop-color="#FFFFFF"/><stop offset="0.91" stop-color="#D9D9D9"/><stop offset="1" stop-color="#D9D9D9"/></linearGradient><clipPath id="clip14"><rect x="-2.5" y="-2.50915" width="129" height="143.022"/></clipPath><clipPath id="clip15"><rect x="0" y="0" width="124" height="137"/></clipPath><linearGradient x1="591.985" y1="219.079" x2="688.015" y2="482.921" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill16"><stop offset="0" stop-color="#70C5F4"/><stop offset="0.18" stop-color="#70C5F4"/><stop offset="0.91" stop-color="#4297D6"/><stop offset="1" stop-color="#4297D6"/></linearGradient><clipPath id="clip17"><rect x="-3.5" y="-3.5" width="102" height="54"/></clipPath><clipPath id="clip18"><rect x="0" y="0" width="94" height="45"/></clipPath><linearGradient x1="610.755" y1="291.347" x2="627.245" y2="336.653" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill19"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#2644B4"/><stop offset="1" stop-color="#2644B4"/></linearGradient><clipPath id="clip20"><rect x="-3.522" y="-3.5" width="88.0503" height="54"/></clipPath><clipPath id="clip21"><rect x="0" y="0" width="80" height="45"/></clipPath><linearGradient x1="597.951" y1="334.007" x2="611.049" y2="369.993" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill22"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#2644B4"/><stop offset="1" stop-color="#2644B4"/></linearGradient><clipPath id="clip23"><rect x="-3.51627" y="-3.5" width="116.037" height="54"/></clipPath><clipPath id="clip24"><rect x="0" y="0" width="108" height="45"/></clipPath><linearGradient x1="622.676" y1="364.008" x2="642.324" y2="417.992" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill25"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#2644B4"/><stop offset="1" stop-color="#2644B4"/></linearGradient><clipPath id="clip26"><rect x="-2" y="-2" width="49" height="73"/></clipPath><clipPath id="clip27"><rect x="0" y="0" width="46" height="69"/></clipPath><linearGradient x1="597.203" y1="258.29" x2="582.797" y2="218.71" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill28"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#5225B5"/><stop offset="1" stop-color="#5225B5"/></linearGradient><clipPath id="clip29"><rect x="-2" y="-2" width="49" height="73"/></clipPath><clipPath id="clip30"><rect x="0" y="0" width="46" height="69"/></clipPath><linearGradient x1="630.203" y1="258.29" x2="615.797" y2="218.71" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill31"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#5225B5"/><stop offset="1" stop-color="#5225B5"/></linearGradient><clipPath id="clip32"><rect x="-2" y="-2" width="49" height="73"/></clipPath><clipPath id="clip33"><rect x="0" y="0" width="46" height="69"/></clipPath><linearGradient x1="664.203" y1="258.29" x2="649.797" y2="218.71" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill34"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#5225B5"/><stop offset="1" stop-color="#5225B5"/></linearGradient><clipPath id="clip35"><rect x="-2" y="-2" width="49" height="73"/></clipPath><clipPath id="clip36"><rect x="0" y="0" width="46" height="69"/></clipPath><linearGradient x1="697.203" y1="258.29" x2="682.797" y2="218.71" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill37"><stop offset="0" stop-color="#057093"/><stop offset="0.18" stop-color="#057093"/><stop offset="0.91" stop-color="#5225B5"/><stop offset="1" stop-color="#5225B5"/></linearGradient></defs><g clip-path="url(#clip9)" transform="translate(-482 -203)"><path d="M540 271.334C540 252.924 554.924 238 573.334 238L706.666 238C725.076 238 740 252.924 740 271.334L740 471.666C740 490.076 725.076 505 706.666 505L573.334 505C554.924 505 540 490.076 540 471.666Z" fill="url(#fill10)" fill-rule="evenodd"/><g clip-path="url(#clip11)" filter="url(#fx0)" transform="matrix(2 0 0 2 520 246)"><g clip-path="url(#clip12)"><path d="M16 30.667C16 22.5666 22.5666 16 30.667 16L89.333 16C97.4334 16 104 22.5666 104 30.667L104 109.133C104 117.233 97.4334 123.8 89.333 123.8L30.667 123.8C22.5666 123.8 16 117.233 16 109.133Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M540 271.334C540 252.924 554.924 238 573.334 238L706.666 238C725.076 238 740 252.924 740 271.334L740 449.666C740 468.076 725.076 483 706.666 483L573.334 483C554.924 483 540 468.076 540 449.666Z" fill="url(#fill13)" fill-rule="evenodd"/><g clip-path="url(#clip14)" filter="url(#fx1)" transform="matrix(2 0 0 1.9927 516 223)"><g clip-path="url(#clip15)"><path d="M13.5 29.7757C13.5 20.8142 20.7382 13.5495 29.667 13.5495L94.333 13.5495C103.262 13.5495 110.5 20.8142 110.5 29.7757L110.5 107.335C110.5 116.296 103.262 123.561 94.333 123.561L29.667 123.561C20.7382 123.561 13.5 116.296 13.5 107.335Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M540 271.334C540 252.924 554.924 238 573.334 238L706.666 238C725.076 238 740 252.924 740 271.334L740 430.666C740 449.076 725.076 464 706.666 464L573.334 464C554.924 464 540 449.076 540 430.666Z" fill="url(#fill16)" fill-rule="evenodd"/><g clip-path="url(#clip17)" filter="url(#fx2)" transform="matrix(2 0 0 2 525 269)"><g clip-path="url(#clip18)"><path d="M19.46 22.5C19.46 20.81 20.83 19.44 22.52 19.44L71.48 19.44C73.17 19.44 74.54 20.81 74.54 22.5L74.54 22.5C74.54 24.19 73.17 25.56 71.48 25.56L22.52 25.56C20.83 25.56 19.46 24.19 19.46 22.5Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M565 314C565 310.686 567.686 308 571 308L667 308C670.314 308 673 310.686 673 314L673 314C673 317.314 670.314 320 667 320L571 320C567.686 320 565 317.314 565 314Z" fill="url(#fill19)" fill-rule="evenodd"/><g clip-path="url(#clip20)" filter="url(#fx3)" transform="matrix(1.9875 0 0 2 525 307)"><g clip-path="url(#clip21)"><path d="M19.7283 22.5C19.7283 20.81 21.107 19.44 22.8076 19.44L57.1925 19.44C58.8931 19.44 60.2717 20.81 60.2717 22.5L60.2717 22.5C60.2717 24.19 58.8931 25.56 57.1925 25.56L22.8076 25.56C21.107 25.56 19.7283 24.19 19.7283 22.5Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M565 352C565 348.686 567.686 346 571 346L638 346C641.314 346 644 348.686 644 352L644 352C644 355.314 641.314 358 638 358L571 358C567.686 358 565 355.314 565 352Z" fill="url(#fill22)" fill-rule="evenodd"/><g clip-path="url(#clip23)" filter="url(#fx4)" transform="matrix(1.99074 0 0 2 525 346)"><g clip-path="url(#clip24)"><path d="M19.4149 22.5C19.4149 20.81 20.7913 19.44 22.4891 19.44L85.5109 19.44C87.2088 19.44 88.5852 20.81 88.5852 22.5L88.5852 22.5C88.5852 24.19 87.2088 25.56 85.5109 25.56L22.4891 25.56C20.7913 25.56 19.4149 24.19 19.4149 22.5Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M565 391C565 387.686 567.686 385 571 385L694 385C697.314 385 700 387.686 700 391L700 391C700 394.314 697.314 397 694 397L571 397C567.686 397 565 394.314 565 391Z" fill="url(#fill25)" fill-rule="evenodd"/><g clip-path="url(#clip26)" filter="url(#fx5)" transform="translate(567 204)"><g clip-path="url(#clip27)"><path d="M31.16 46.23C31.16 50.7367 27.5066 54.39 23 54.39L23 54.39C18.4933 54.39 14.84 50.7367 14.84 46.23L14.84 22.77C14.84 18.2634 18.4933 14.61 23 14.61L23 14.61C27.5066 14.61 31.16 18.2634 31.16 22.77Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M598 250C598 254.418 594.418 258 590 258L590 258C585.582 258 582 254.418 582 250L582 227C582 222.582 585.582 219 590 219L590 219C594.418 219 598 222.582 598 227Z" fill="url(#fill28)" fill-rule="evenodd"/><g clip-path="url(#clip29)" filter="url(#fx6)" transform="translate(600 204)"><g clip-path="url(#clip30)"><path d="M31.16 46.23C31.16 50.7367 27.5066 54.39 23 54.39L23 54.39C18.4933 54.39 14.84 50.7367 14.84 46.23L14.84 22.77C14.84 18.2634 18.4933 14.61 23 14.61L23 14.61C27.5066 14.61 31.16 18.2634 31.16 22.77Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M631 250C631 254.418 627.418 258 623 258L623 258C618.582 258 615 254.418 615 250L615 227C615 222.582 618.582 219 623 219L623 219C627.418 219 631 222.582 631 227Z" fill="url(#fill31)" fill-rule="evenodd"/><g clip-path="url(#clip32)" filter="url(#fx7)" transform="translate(634 204)"><g clip-path="url(#clip33)"><path d="M31.16 46.23C31.16 50.7367 27.5066 54.39 23 54.39L23 54.39C18.4933 54.39 14.84 50.7367 14.84 46.23L14.84 22.77C14.84 18.2634 18.4933 14.61 23 14.61L23 14.61C27.5066 14.61 31.16 18.2634 31.16 22.77Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M665 250C665 254.418 661.418 258 657 258L657 258C652.582 258 649 254.418 649 250L649 227C649 222.582 652.582 219 657 219L657 219C661.418 219 665 222.582 665 227Z" fill="url(#fill34)" fill-rule="evenodd"/><g clip-path="url(#clip35)" filter="url(#fx8)" transform="translate(667 204)"><g clip-path="url(#clip36)"><path d="M31.16 46.23C31.16 50.7367 27.5066 54.39 23 54.39L23 54.39C18.4933 54.39 14.84 50.7367 14.84 46.23L14.84 22.77C14.84 18.2634 18.4933 14.61 23 14.61L23 14.61C27.5066 14.61 31.16 18.2634 31.16 22.77Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M698 250C698 254.418 694.418 258 690 258L690 258C685.582 258 682 254.418 682 250L682 227C682 222.582 685.582 219 690 219L690 219C694.418 219 698 222.582 698 227Z" fill="url(#fill37)" fill-rule="evenodd"/></g></svg>