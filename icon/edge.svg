<svg viewBox="0,0,312,316" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.55556 5.58164"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.58025 5.55556"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.490196" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.55556 5.55556"/></filter><clipPath id="clip3"><rect x="481" y="207" width="312" height="316"/></clipPath><clipPath id="clip4"><rect x="-6.5" y="-6.53052" width="129" height="119.056"/></clipPath><clipPath id="clip5"><rect x="0" y="0" width="118" height="107"/></clipPath><linearGradient x1="757.125" y1="492.658" x2="564.874" y2="331.341" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill6"><stop offset="0" stop-color="#61A2F9"/><stop offset="0.18" stop-color="#61A2F9"/><stop offset="0.91" stop-color="#2D42DB"/><stop offset="1" stop-color="#2D42DB"/></linearGradient><clipPath id="clip7"><rect x="-6.5289" y="-6.5" width="125.053" height="142"/></clipPath><clipPath id="clip8"><rect x="0" y="0" width="113" height="131"/></clipPath><linearGradient x1="615.095" y1="263.026" x2="571.904" y2="507.973" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill9"><stop offset="0" stop-color="#775CFE"/><stop offset="0.18" stop-color="#775CFE"/><stop offset="0.51" stop-color="#6089FC"/><stop offset="1" stop-color="#6089FC"/></linearGradient><clipPath id="clip10"><rect x="-6.5" y="-6.5" width="164" height="125"/></clipPath><clipPath id="clip11"><rect x="0" y="0" width="154" height="114"/></clipPath><linearGradient x1="764.958" y1="209.648" x2="515.043" y2="419.352" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill12"><stop offset="0" stop-color="#C45DD5"/><stop offset="0.18" stop-color="#C45DD5"/><stop offset="0.69" stop-color="#9F65E5"/><stop offset="1" stop-color="#9F65E5"/></linearGradient></defs><g clip-path="url(#clip3)" transform="translate(-481 -207)"><g clip-path="url(#clip4)" filter="url(#fx0)" transform="matrix(2 0 0 1.99065 543 311)"><g clip-path="url(#clip5)"><path d="M97.6006 59.7266C96.0091 60.5574 94.3679 61.2884 92.6864 61.9155 87.3621 63.9063 81.724 64.916 76.0429 64.8961 54.1142 64.8961 35.0136 49.7601 35.0136 30.2928 35.0717 24.985 37.9793 20.1227 42.6168 17.5785 22.7744 18.4168 17.6747 39.1881 17.6747 51.3436 17.6747 85.8072 49.2464 89.2535 56.0614 89.2535 59.7239 89.2535 65.2408 88.1823 68.5788 87.1112L69.1815 86.9249C81.9557 82.4901 92.8456 73.8173 100.058 62.3346 100.587 61.4538 100.305 60.3087 99.4283 59.7771 98.8702 59.4388 98.1763 59.4196 97.6006 59.7265Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M749.737 426.439C746.078 428.34 742.306 430.012 738.44 431.447 726.2 436.002 713.239 438.313 700.179 438.267 649.768 438.267 605.859 403.634 605.859 359.091 605.992 346.946 612.677 335.821 623.338 329.999 577.723 331.918 565.999 379.445 565.999 407.257 565.999 486.114 638.578 494 654.245 494 662.664 494 675.347 491.549 683.02 489.098L684.406 488.671C713.772 478.524 738.806 458.68 755.386 432.406 756.602 430.391 755.954 427.771 753.939 426.554 752.656 425.78 751.061 425.736 749.737 426.439Z" fill="url(#fill6)" fill-rule="evenodd"/><g clip-path="url(#clip7)" filter="url(#fx1)" transform="matrix(1.99115 0 0 2 481 260)"><g clip-path="url(#clip8)"><path d="M66.4105 106.725C62.279 104.167 58.6979 100.818 55.8744 96.871 43.6707 80.2228 47.3187 56.8665 64.0223 44.7034 65.77 43.4307 67.6251 42.3115 69.5667 41.3582 71.0519 40.6643 73.5119 39.4615 76.8074 39.5078 81.5081 39.544 85.9213 41.769 88.7359 45.5217 90.6034 48.0248 91.6271 51.0533 91.66 54.1724 91.66 54.0799 103.032 17.3489 54.5284 17.3489 34.1523 17.3489 17.3967 36.5933 17.3967 53.5247 17.3156 62.4686 19.2341 71.3181 23.0128 79.4307 35.8221 106.616 67.0229 119.938 95.6053 110.425 85.8144 113.507 75.1599 112.155 66.4569 106.725Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M616.182 480.461C606.726 474.581 598.53 466.882 592.068 457.809 564.138 419.537 572.487 365.845 610.716 337.883 614.716 334.958 618.962 332.385 623.406 330.193 626.805 328.598 632.435 325.833 639.977 325.939 650.736 326.023 660.836 331.138 667.278 339.764 671.552 345.519 673.895 352.481 673.97 359.651 673.97 359.438 699.996 274.999 588.988 274.999 542.353 274.999 504.005 319.24 504.005 358.162 503.82 378.723 508.21 399.067 516.859 417.716 546.175 480.211 617.584 510.836 683 488.968 660.591 496.054 636.207 492.944 616.288 480.461Z" fill="url(#fill9)" fill-rule="evenodd"/><g clip-path="url(#clip10)" filter="url(#fx2)" transform="matrix(2 0 0 2 486 206)"><g clip-path="url(#clip11)"><path d="M88.3581 86.2463C87.9391 86.708 86.6355 86.8542 86.7751 88.8317 86.9148 90.8092 87.7529 91.6017 89.1961 92.5713 95.8538 97.188 102.791 96.2108 108.331 96.1723 113.871 96.1339 118.166 94.8458 122.438 92.3404 131.189 87.2645 136.575 77.9716 136.591 67.9179 136.731 57.5764 132.867 50.6974 131.33 47.6503 121.414 28.537 100.137 17.5492 76.9981 17.5492 44.4103 17.546 17.8633 43.5005 17.405 75.8125 17.6378 58.9614 34.538 45.342 54.6507 45.342 56.2802 45.342 65.5917 45.4805 74.2047 49.9587 80.2558 52.9404 85.2668 57.6522 88.5909 63.4858 91.4309 68.3795 91.943 74.6121 91.943 77.1052 91.943 79.5982 90.6859 83.2455 88.3116 86.2925L88.3581 86.2463Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M666.111 381.924C665.147 382.986 662.151 383.322 662.472 387.868 662.793 392.414 664.719 394.236 668.037 396.465 683.342 407.078 699.289 404.831 712.026 404.743 724.762 404.654 734.634 401.693 744.455 395.934 764.573 384.265 776.954 362.902 776.992 339.79 777.313 316.017 768.429 300.203 764.897 293.198 742.1 249.259 693.189 224 639.996 224 565.081 223.993 504.054 283.658 503 357.939 503.535 319.201 542.386 287.891 588.622 287.891 592.368 287.891 613.774 288.21 633.574 298.505 647.485 305.359 659.004 316.191 666.646 329.601 673.174 340.851 674.352 355.179 674.352 360.91 674.352 366.641 671.462 375.026 666.004 382.031L666.111 381.924Z" fill="url(#fill12)" fill-rule="evenodd"/></g></svg>