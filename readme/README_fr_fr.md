<p align="center">
    <img src="./../icon/windows12.svg" width="100" height="100">
</p>
<h1 align="center">Windows 12 Online</h1>
<p align="center" class="shields">
  <a href="https://github.com/tjy-gitnub/win12/issues" style="text-decoration:none">
    <img src="https://img.shields.io/github/issues/tjy-gitnub/win12.svg" alt="GitHub issues"/>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/stargazers" style="text-decoration:none">
    <img src="https://img.shields.io/github/stars/tjy-gitnub/win12.svg" alt="GitHub stars"/>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/network" style="text-decoration:none">
    <img src="https://img.shields.io/github/forks/tjy-gitnub/win12.svg" alt="GitHub forks"/>
  </a>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/blob/master/LICENSE" style="text-decoration:none">
    <img src="https://img.shields.io/github/license/tjy-gitnub/win12" alt="GitHub License"/>
  </a>
</p>
<p align="center" class="language" title="Language selection 语言选择">
  <a href="README_en_us.md">English</a> | 
  <a href="README_zh_cn.md">简体中文</a> | 
  <b>Français</b>
</p>
<details align="center">
  <summary>Star History</summary>
  <a href="https://star-history.com/#tjy-gitnub/win12&Date" style="text-decoration:none">
    <img src="https://api.star-history.com/svg?repos=tjy-gitnub/win12&type=Date" alt="Star History Chart">
  </a>
</details>

## Annonces
### C’est important !
Gratuitement, nous demandons sincèrement à tous les utilisateurs compétents de réaliser des vidéos promotionnelles pour nous !
Pour plus de détails, reportez-vous à [Annonce vidéo promotionnelle(chinois)](https://github.com/tjy-gitnub/win12/wiki/%E5%AE%A3%E4%BC%A0%E7%89%87%E5%85%AC%E5%91%8A)

---

- [Windows 12 Web Edition](#windows-12-web-edition)
	- [préface précédente](#préface-précédente)
	- [préface](#préface)
		- [remerciements spéciaux](#remerciements-spéciaux)
	- [affichage des effets](#affichage-des-effets)
	- [expérience en ligne](#expérience-en-ligne)
	- [installation et utilisation](#installation-et-utilisation)
	- [planification des perspectives](#planification-des-perspectives)
	- [description de l'Open Source](#description-de-lopen-source)
	- [Note sur les contributions](#note-sur-les-contributions)

## préface précédente

Récemment (pas longtemps auparavant), nous avons remarqué qu'il y avait quelques imposteurs, nous tenons donc à faire une déclaration :

Notre projet est développé conjointement par tjy-gitnub (troisième), NB-group (préparation), 782 (première année), les trois développeurs !

(Si vous souhaitez créer une vidéo promotionnelle pour nous, veuillez également inclure nos comptes Bilibili)

Nous n'avons de comptes officiels que sur GitHub, Bilibili et Aifadian !

GitHub：

Allez voir dans les contributeurs~

Bilibili：

tjy-gitnub：

![image](https://github.com/tjy-gitnub/win12/assets/121747915/6b13f81a-2a33-4265-abee-44c3796c2817)

NB-group：

![image](https://github.com/tjy-gitnub/win12/assets/121747915/9dad6cac-e0e7-44b3-975e-41eaf33520dd)

782：

![image](https://github.com/tjy-gitnub/win12/assets/121747915/e475890f-010d-4e47-9ac6-fd4abad26218)

afdian.com：

Le seul compte officiel au monde où vous pouvez faire un don ! Ouvrez bien vos yeux ! Ne faites pas de dons à d'autres personnes !

![image](https://github.com/tjy-gitnub/win12/assets/121747915/c4a7e71c-ac41-4ab5-ba87-967d188ca2cc)

URL：<https://afdian.com/a/qstudio>

**Notre projet et notre code source ne sont absolument pas soumis à des frais obligatoires ! Si vous voyez que ce projet est vendu sans mentionner le lien vers le projet d'origine, ou s'il enfreint la licence open source EPL-2.0 et les instructions open source de ce projet, n'hésitez pas à le signaler !**

## préface

Récemment (il y a longtemps), inspiré par la version conceptuelle de Windows 12 (Powered by PowerPoint), j'ai décidé de créer une version Web de Windows 12, comme  [Win11 in React](https://win11.blueedge.me/).

Référence [planification des perspectives](#planification-des-perspectives)

Un design d'interface utilisateur élégant, des animations fluides et riches, toutes sortes de fonctionnalités avancées (par rapport à la version Web).

> L'adaptation pour les appareils mobiles n'est pas très optimale, utilisez plutôt la version de bureau du site >v-)o

### remerciements spéciaux

Un grand merci aux sponsors suivants :

- CursoR_光标（<https://afdian.com/a/cursor>）
- Baymax（<https://afdian.com/u/a131cd504dea11eeb6be5254001e7c00>）

Nous tenons à remercier publiquement ces sponsors !

## affichage des effets

> La nouvelle version a beaucoup de changements, à titre indicatif seulement, veuillez vous référer à la réalité （cliquez [cic](https://tjy-gitnub.github.io/win12/desktop.html) , ce n'est pas compliqué -_-)o

![image](https://tjy-gitnub.github.io/win12/img/start-menu.png)

*Menu Démarrer*

![image](https://tjy-gitnub.github.io/win12/img/colorful-apps.png)

*Applications colorées*

![image](https://tjy-gitnub.github.io/win12/img/dark-mode.png)

*Mode sombre*

![image](https://tjy-gitnub.github.io/win12/img/ai-copilot.png)

*AI Copilot*

## expérience en ligne

[Aperçu en ligne](https://tjy-gitnub.github.io/win12/desktop.html)

Cela peut être un peu lent, veuillez patienter~

## installation et utilisation

Téléchargez le code, aucune installation requise, ouvrez `desktop.html`.

## planification des perspectives

Concernant la planification de ce projet :

- [x] Fonctions de base et applications
- [x] Optimisation générale de l'interface
- [x] Ajout d'effets spéciaux
- [x] Fonctionnalités de fenêtre
- [x] Amélioration des applications
- [x] Ajout de paramètres de personnalisation
- [x] Ajout de l'application Edge
- [ ] Ajout d'onglets pour plus d'applications
- [x] Amélioration des widgets, ajout sur le bureau, etc.
- [x] Fond d'écran dynamique
- [ ] Personnalisation avancée de la barre des tâches
- [ ] Expansion de l'écosystème d'applications, ajout de la Microsoft Store
- [ ] Amélioration des paramètres et des mises à jour de Windows

Voici quelques idées farfelues ~o~)/ :

- [x] Création du système de fichiers
- [ ] Création de son propre mécanisme de fichiers exécutables
- [ ] Conversion et exécution des fichiers .exe
- [ ] Fourniture de plus d'API pour les applications
- [x] Noyau de navigateur intégré, devenant une application
- [ ] Renommer le projet en "Windows 12"
- [ ] Intégration dans le système Windows
- [ ] Définir le programme de démarrage comme cette application
- [ ] Supprimer les fonctionnalités système superflues, les encapsuler dans un système d'exploitation indépendant
- [ ] Renommer le projet en "Doswin 1.0"
- [ ] Adaptation aux ordinateurs quantiques
- [x] Intégration de ChatGPT
- [ ] Renommer le projet en "550W"

## description de l'open source

Auteur de ce projet : Tan Jingyuan (tjy-gitnub)

Lien vers ce projet ：<https://github.com/tjy-gitnub/win12>

Ce projet est open source. Il utilise la licence open source EPL v2.0. Les licences open source sont des contrats juridiquement contraignants, veuillez respecter la licence open source et le travail des autres.

Conformément à la licence, vous pouvez diffuser, distribuer, modifier et publier à nouveau ce projet, à des fins personnelles et commerciales, mais nous ne encourageons pas toutes les utilisations commerciales.

Vous devez fournir la source, **y compris l'auteur, le lien vers le projet** (voir ci-dessus), etc., et vous devez utiliser la même licence open source.

Si le code source de ce projet est publié en même temps que votre code source privé en tant que partie du projet, vous pouvez utiliser une autre licence, **mais vous devez indiquer que la partie EPL continue de suivre la licence EPL**.

Pas utilisé comme base pour ajouter ou modifier le projet, uniquement consulté à partir du code source, n'a pas besoin d'être open source, mais est également destiné à un usage éducatif.

En raison d'une utilisation abusive constatée récemment, des dispositions supplémentaires sont stipulées ici :

- Toute personne utilisant ce projet doit inclure le nom de l'auteur d'origine et le lien vers le projet dans l'introduction. Il est interdit de délibérément dissimuler les informations de crédit, le nom de l'auteur d'origine ou le lien vers le projet, et il est interdit de restreindre l'accès aux informations de crédit, au nom de l'auteur d'origine ou au lien vers le projet dans le projet d'origine, et il est également interdit de modifier les informations de crédit dans le projet d'origine ;

- Toute personne utilisant ce projet à des fins commerciales doit **mentionner l'auteur d'origine ainsi que le lien vers le projet**, et cela doit être open source conformément à la licence EPL. Le code source non modifié ne doit **pas être utilisé à des fins** commerciales ;

- Toute personne utilisant ce projet ne doit pas supprimer ni intentionnellement masquer, restreindre la visibilité de cette déclaration ;

- Nous vous prions de respecter les règles susmentionnées. Nous nous réservons le droit de prendre des mesures légales pour protéger nos droits légitimes.

## Note sur les contributions

Veuillez consulter [Guide de contribution(chinois)](./CONTRIBUTING.md)pour plus de détails.

> Traduit par [@Junchen Yi](https://github.com/Jimmy-Effe)
