<p align="center">
    <img src="./../icon/windows12.svg" width="100" height="100">
</p>
<h1 align="center">Windows 12 Online</h1>
<p align="center" class="shields">
  <a href="https://github.com/tjy-gitnub/win12/issues" style="text-decoration:none">
    <img src="https://img.shields.io/github/issues/tjy-gitnub/win12.svg" alt="GitHub issues"/>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/stargazers" style="text-decoration:none">
    <img src="https://img.shields.io/github/stars/tjy-gitnub/win12.svg" alt="GitHub stars"/>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/network" style="text-decoration:none">
    <img src="https://img.shields.io/github/forks/tjy-gitnub/win12.svg" alt="GitHub forks"/>
  </a>
  </a>
  <a href="https://github.com/tjy-gitnub/win12/blob/master/LICENSE" style="text-decoration:none">
    <img src="https://img.shields.io/github/license/tjy-gitnub/win12" alt="GitHub License"/>
  </a>
</p>
<p align="center" class="language" title="Language selection 语言选择">
  <b>English</b> | 
  <a href="README_zh_cn.md">简体中文</a> | 
  <a href="README_fr_fr.md">Français</a>
</p>
<details align="center">
  <summary>Star History</summary>
  <a href="https://star-history.com/#tjy-gitnub/win12&Date" style="text-decoration:none">
    <img src="https://api.star-history.com/svg?repos=tjy-gitnub/win12&type=Date" alt="Star History Chart">
  </a>
</details>

## Announcement
### Important!
Free of charge, we sincerely ask all capable users to make promotional videos for us!
For details, please refer to [Promotional Video Announcement(Chinese)](https://github.com/tjy-gitnub/win12/wiki/%E5%AE%A3%E4%BC%A0%E7%89%87%E5%85%AC%E5%91%8A)


- [Windows 12 Web Edition](#windows-12-web-edition)
  - [Introduction Before Introduction](#introduction-before-introduction)
  - [Introduction](#introduction)
    - [Particular Thanks](#particular-thanks)
  - [Preview](#preview)
  - [Use Online](#use-online)
  - [Installation And Use](#installation-and-use)
  - [Subsequent Planning](#subsequent-planning)
  - [Open Source Description](#open-source-description)
  - [How To Contribute](#how-to-contribute)

## Introduction Before Introduction

Recently, instances of impersonation by others claiming to be associated with us have come to our attention. Therefore, a statement is hereby issued:

This project was developed by tjy-gitnub（Grade 9），NB-group（Prospective junior high school students），782（Grade 7）, three developers.！

（If you're going to make a promotional video for us, please hook up our Bilibili account as well!

We only have official accounts on GitHub, Bilibili and afdian.com！

GitHub：

Check out the contributors

Bilibili account：

tjy-gitnub：

![image](https://github.com/tjy-gitnub/win12/assets/*********/6b13f81a-2a33-4265-abee-44c3796c2817)

NB-group：

![image](https://github.com/tjy-gitnub/win12/assets/*********/9dad6cac-e0e7-44b3-975e-41eaf33520dd)

782：

![image](https://github.com/tjy-gitnub/win12/assets/*********/e475890f-010d-4e47-9ac6-fd4abad26218)

afdian.com：

The only official account in the world where you can donate! Shine your eyes! Don't donate to anyone else!

![image](https://github.com/tjy-gitnub/win12/assets/*********/c4a7e71c-ac41-4ab5-ba87-967d188ca2cc)

URL：<https://afdian.com/a/qstudio>

**This project and source code is absolutely no mandatory charges! If you see the sale of this project and did not specify the original project link, or violation of the EPL-2.0 open source agreement and the project open source description, welcome to report!**

## Introduction

Recently (a long time ago), inspired by the concept of Windows 12 (Powered by PowerPoint), I decided to create a web version of Windows 12, similar to [Win11 in React](https://win11.blueedge.me/).

Consultation [Preview](#preview).

Beautiful ui design, smooth and rich animations, various advanced features (compared to the web version).

> Adaptation for mobile is not too perfect, just use desktop `>v-)o`

### Particular Thanks

Special thanks to the following sponsors.

- CursoR_光标（<https://afdian.com/a/cursor>）
- Baymax（<https://afdian.com/u/a131cd504dea11eeb6be5254001e7c00>）

Hereby, we express public gratitude to the above sponsors!

## Preview

> There are a lot of changes in the new version, it is for reference only, please refer to the real thing!（Click [Here](https://tjy-gitnub.github.io/win12/desktop.html) you will know，it's no trouble`-_-)o` ）

![image](https://tjy-gitnub.github.io/win12/img/start-menu.png)

*start menu*

![image](https://tjy-gitnub.github.io/win12/img/colorful-apps.png)

*colorful apps*

![image](https://tjy-gitnub.github.io/win12/img/dark-mode.png)

*dark mode*

![image](https://tjy-gitnub.github.io/win12/img/ai-copilot.png)

*AI Copilot*

## Use Online

[Use Online](https://tjy-gitnub.github.io/win12/desktop.html)

It's a little slow. Wait a minute.

## Installation And Use

Download the code, no need to install, just open `desktop.html`.

## Subsequent Planning

Regarding the project's route planning.

- [x] Basic features and applications
- [x] Overall appearance optimization
- [x] Integration of special effects
- [x] Window functionalities
- [x] Application refinement
- [x] Addition of more personalized settings
- [x] Inclusion of Edge application
- [ ] Adding tabbed browsing for more applications
- [x] Improvement of widgets, addition to desktop, and other features
- [x] Dynamic wallpaper
- [ ] Further customization options for the taskbar
- [ ] Enrichment of the application ecosystem, addition of Microsoft Store
- [ ] Refinement of settings and Windows updates

Below are some... um... imaginative thoughts. `~o~)/`：

- [x] Establish file system
- [ ] Develop a proprietary executable file mechanism
- [ ] Convert and execute .exe files
- [ ] Provide more APIs for application integration
- [x] Embed a browser engine as an application
- [ ] Rename the project to "Windows 12"
- [ ] Integrate into the Windows operating system
- [ ] Set this application as the default startup program
- [ ] Streamline unnecessary system functions and encapsulate as an independent operating system
- [ ] Rename the project to "Doswin 1.0"
- [ ] Adapt for quantum computing
- [x] Integrate with ChatGPT
- [ ] Rename the project to "550W"

## Open Source Description

The developer of this project：谭景元（tjy-gitnub）

Link to this item：<https://github.com/tjy-gitnub/win12>

This project is an open-source initiative. It utilizes the EPL v2.0 open-source license. Open-source licenses are legally binding agreements; please voluntarily comply with the open-source license and respect the efforts of others.

Under the license, you are allowed to propagate, distribute, modify, and re-publish this project, including for both personal and commercial purposes. However, we do not encourage any and all commercial uses.

You must give the source code source, **including the author, a link to the project** (see above), etc., and it must be open-sourced using the same protocol.

If the source code for this project is distributed as part of a project with your private source code, you may use other protocols **but declare the contents of the EPL section and state that this section continues to follow the EPL protocol**.

Not on the basis of the project to add, modify, only reference to the source code, do not need open source, but also for learning purposes only.

Due to the large number of abuses detected in the recent past, additional regulations are hereby stipulated:

- Users of this project must include the original author's name and project link in the introduction. Intentionally concealing attribution information, the original author, or project link from the original project is prohibited. Viewing the attribution information, original author, or project link in the original project should not be restricted, and the attribution information in the original project must not be modified.

- Those using this project for commercial purposes must **clearly indicate the original author and project link** and **must**open-source it under the EPL license. The unmodified source code**cannot be used for**commercial purposes.；

- Users of this project are not allowed to remove or intentionally hide, restrict the visibility of this statement.

- Please comply with the above provisions; we reserve the right to pursue legal actions to protect our rights if necessary.

## How-To-Contribute

For details, please see [How To Contribute(Chinese)](../CONTRIBUTING.md).

> Translated by [@Junchen Yi](https://github.com/Jimmy-Effe)