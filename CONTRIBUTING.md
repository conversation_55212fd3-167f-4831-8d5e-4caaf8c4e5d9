# 贡献指南

感谢所有为本项目做出贡献的开发者和大家的支持！

如果您喜欢本项目，欢迎给这个项目点一颗Star、给我们赞助一下或为我们做出一些重要的贡献qwq~

请不要提交无意义的PR哦~这里点名批评：
<img width="673" alt="image" src="https://github.com/tjy-gitnub/win12/assets/121747915/2da6f2d8-369a-4ef7-a87e-7ac4ecacd78b">

## 漏洞汇报

1. 请使用最新版本，以确认该漏洞是否已经被修复。

2. 请确认该漏洞或错误是不是属于您所使用的问题。如，使用较为老旧的浏览器（如IE等）、关闭了浏览器部分特性（如禁止存储数据等）

3. 请清楚地描述漏洞，以便我们更好地进行修复。

4. 您可以使用“漏洞汇报”Issue模板进行反馈，但请正确地填写其中的内容。

5. 禁止在内容中添加任何违反法律或政治敏感的内容，否则将会采取锁定+视情况封禁的处理。

## 提出建议

1. 请使用最新版本，以确认该建议是否已被实现/解决。

2. 请清楚地描述建议，以便我们更好地进行实现/解决。

3. 您可以使用“提出建议”Issue模板进行反馈，但请正确地填写其中的内容。

4. 禁止在内容中添加任何违反法律或政治敏感的内容，否则将会采取锁定+视情况封禁的处理。

## 提交代码

注意：请严格按照下面的，以及desktop.html开头的开发规范编辑代码，否则不予合并

1. 请尽量一次Commit提交全部内容。可以追加Commits，但尽量不要超过5个Commits。

2. 请尽量使用Git命令行、Github Desktop、[https://github.dev](https://github.dev/tjy-gitnub/win12)等方式进行提交。请不要直接在浏览器中上传文件来提交。

3. 禁止上传任何违反法律或政治敏感的内容，否则将会采取锁定+视情况封禁的处理（温馨提示：时事新闻也不行）。

4. 提交时请不要随意取提交标题及内容，例如：

   - 好的例子：修复xx无法正常使用的问题、新增xx应用
  
   - 坏的例子：阿巴阿巴、这个玩意忘弄了、bug太多了...qwq

5. 格式化要求:

   - 请不要使用格式化工具格式化HTML文件

   - 对于JavaScript和CSS文件，可以使用Visual Studio Code自带的格式化工具格式化

### 提交信息要求

   1. 若更新具有一定重要性或量级时，请按照以下格式：

      ```
      v11.4.5 - 更新了xxx

      (更新来自 @Somebody)
      - 更新了...
      - 优化了...
      - 修复了...
      ...
      ```

      - 使用该格式的要求：

         1. 该更新提交前必须告知我们。

      - 说明：

         1. 标题要带有版本号、主要更新内容。

         2. 内容第一行注明更新来源。

         3. 内容要用列表的方式阐述更新内容。

      - 注意：

         1. 请不要任意选取版本号。若您不清楚，可以通过我们的交流群与我们取得联系（<https://teams.live.com/l/invite/FEA0yrNkE_bAn-ddwI>）并分配到版本号。

         2. 在更新时，记住要在“关于 Windows 12 网页版”应用的更新记录中，添加关于该更新的相关内容。

   2. 若满足下列条件，提交内容不作过于标准的规定：

      - 更新内容较少。

      - 更新内容没有重要的更改。

      尽管没有标准的规定，但仍然需要：

         1. 提交标题应当清晰明了，能简要概括更新的主要内容。

         2. 提交内容应当注明提交者，并以列表或其他方式进行对本次更新内容的阐述。

### 开发规范

1. 对于HTML文件的规定

   详见 `desktop.html` 开头的代码规范，务必认真阅读。

2. 对JS文件的规定

   1. 请按照以下代码风格进行开发：

   ```js
      var sum = 0;
      for (var i = 0; i < 10; i++) {
         sum += i;
      }
      console.log(sum);
   ```

   2. 对于函数名及变量命名，请使用驼峰式命名法，如：

      - isLoaded

      - storagedItems

   3. 对于类名，请使用帕斯卡命名法（大驼峰式命名法），如：

      - WindowManager

      - Widgets

   4. 对于代码规范的规定：

      1. 对于那些不需要展开的代码，尽量压成一行