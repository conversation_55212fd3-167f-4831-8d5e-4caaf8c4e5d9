# 更新记录
## v1.0.0~v7.4.2
> *v7.4.2之后的更新记录请前往“关于 Windows 12 网页版”应用内查看*


<details><summary><span>v7.4.2</span> AI Copilot 3.0！</summary><p>
	&emsp;&emsp;-(更新来自@NB-Group)<br>
	&emsp;&emsp;-笑死，昨天刚修好就又寄了，这次的船新版本绝对稳定，（理论上）再也不会寄</p>
</p></details>
<details><summary><span>v7.4.1</span> AI Copilot 2.0！</summary><p>
	&emsp;&emsp;-(更新来自@NB-Group)<br />
	&emsp;&emsp;-修复了ai无法使用的问题</p>
</p></details>
<details><summary><span>v7.4.0</span> AI Copilot！</summary><p>
	&emsp;&emsp;-(更新来自@NB-Group)<br>
	&emsp;&emsp;-去掉一些奇怪的东西<br />
	&emsp;&emsp;-经过我114年的开发，AI Copilot 2.0诞生了！<br />
	&emsp;&emsp;-开源了AI后端的代码<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v7.3.7</span> 细节更改</summary><p>
	&emsp;&emsp;-加入一些奇怪的东西<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v7.3.6</span> 语音识别球</summary><p>
	&emsp;&emsp;(更新来自@NB-Group)<br/>
	&emsp;&emsp;-增加语音识别球<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v7.3.5</span> 整顿代码</summary><p>
	&emsp;&emsp;-整顿代码，贡献者必须按本文件开头以及CONTRIBUTING.md的规范编辑代码，否则不予合并，感谢理解<br/>
	&emsp;&emsp;-重绘新增的一些图标<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v7.3.4</span> Edge更新</summary><p>
	&emsp;&emsp;(更新来自@lh11117)<br>
	&emsp;&emsp;-Edge 支持全屏<br/>
	&emsp;&emsp;-Edge 支持收藏夹</p>
</p></details>
<details><summary><span>v7.3.3</span> 细节优化</summary><p>
	&emsp;&emsp;细节优化</p>
</p></details>
<details><summary><span>v7.3.2</span> 细节更改</summary><p>
	&emsp;&emsp;(更新来自 @tjy-gitnub )<br />
	&emsp;&emsp;-新增了部分本地化语言功能<br />
	&emsp;&emsp;-更新壁纸<br />
	&emsp;&emsp;-优化样式<br />
	&emsp;&emsp;-优化shutdown命令<br />
	&emsp;&emsp;-其他更新与优化<br />
	&emsp;&emsp;-修复了点击标签页无法切换焦点的问题<br />
	&emsp;&emsp;-各位可以抽时间去看看README.md，一些特殊情况</p>
</p></details>
<details><summary><span>v7.3.1</span> Bug修复与细节优化</summary><p>
	&emsp;&emsp;-删除changelog.md中重复的字(更新来自 @ZigaoWang )<br />
	&emsp;&emsp;-修复BIOS无法正确访问的问题(更新来自 @ludonghengsb )</p>
</p></details>
<details><summary><span>v7.3.0</span> 桌面小组件、Dock自动隐藏、计算器完善和Windows 更新</summary><p>
	&emsp;&emsp;(更新来自 @User782Tec )<br />
	&emsp;&emsp;-新增了桌面小组件，可将小组件添加到桌面并调整位置<br />
	&emsp;&emsp;-新增了编辑模式，可快捷编辑小组件的位置，并提供了相关的快捷入口<br />
	&emsp;&emsp;-新增了窗口全屏且Dock不在使用时自动隐藏的功能<br />
	&emsp;&emsp;-为Microsoft Edge浏览器和文件资源管理器新增了后退/前进的功能<br />
	&emsp;&emsp;-又双叒叕优化计算器的精度问题与安全性问题<br />
	&emsp;&emsp;-重写更新机制，并完善 Windows 更新<br />
	&emsp;&emsp;-修复了文件资源管理器中右键文件失效的问题<br />
	&emsp;&emsp;-修复了文件资源管理器中无法正确显示文件夹中文件的问题<br />
	&emsp;&emsp;-更换了Python Editor的图标<br />
	&emsp;&emsp;-完善了开源说明<br />
	&emsp;&emsp;-其他细节与修复</p>
</p></details>
<details><summary><span>v7.2.1</span> README更新、“关于”应用显示实时Star数量、个性化设置支持保存</summary><p>
	&emsp;&emsp;(更新来自 @lh11117 )<br />
	&emsp;&emsp;-README更新<br />
	&emsp;&emsp;-“关于”应用显示实时Star数量<br />
	&emsp;&emsp;-个性化设置支持保存<br /></p>
</p></details>
<details><summary><span>v7.2.0</span> 优化运行、添加Windows12应用、添加WSA</summary><p>
	&emsp;&emsp;(更新来自 @lh11117 )<br />
	&emsp;&emsp;-优化运行<br />
	&emsp;&emsp;-添加Windows12应用<br />
	&emsp;&emsp;-添加WSA<br />
	&emsp;&emsp;-添加shutdown命令<br />
	&emsp;&emsp;-添加BIOS系统<br />
	&emsp;&emsp;-支持桌面图标删除<br />
	&emsp;&emsp;-更丰富的文件目录<br />
	&emsp;&emsp;-任务管理器支持置顶<br /></p>
</p></details>
<details><summary><span>v7.1.0</span> 计算器进一步修复、离线页面的深色模式</summary><p>
	&emsp;&emsp;(更新来自 @lh11117)<br />
	&emsp;&emsp;-进一步修复了计算器精度缺陷和接受输入非法内容的问题<br />
	&emsp;&emsp;-为Microsoft Edge浏览器的离线界面添加了深色模式版本<br />
	&emsp;&emsp;-其他一些细节修复<br /></p>
</p></details>
<details><summary><span>v7.0.2</span> 计算器修复</summary><p>
	&emsp;&emsp;(更新来自 @cong1223 和 @iewnfod)<br />
	&emsp;&emsp;-修复计算器计算加减乘除、平方、平方根等运算的精度问题(来自 @cong1223)<br />
	&emsp;&emsp;-只允许calc-input输入数字(来自 @iewnfod)<br /></p>
</p></details>
<details><summary><span>v7.0.1</span> 资源管理器标签页、离线界面与新闻</summary><p>
	&emsp;&emsp;-文件资源管理器的标签页(来自 @tjy-gitnub)<br />
	&emsp;&emsp;-新增了Microsoft Edge浏览器的离线界面(来自 @User782Tec)<br />
	&emsp;&emsp;-新增新闻(来自 @Nick-DL)<br />
	&emsp;&emsp;-优化部分UI<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v7.0.0</span> AI助手,新增多个应用与功能</summary><p>
	&emsp;&emsp;-Windows 12 AI Copilot(@NB-Group提供api, @tjy-gitnub魔改)<br />
	&emsp;&emsp;-任务管理器(由 @User782Tec 提供)<br />
	&emsp;&emsp;-安全中心(由 @NB-Group 提供)<br />
	&emsp;&emsp;-适配文件系统(由 @NB-Group 提供)(别问我为啥现在才出，要问问@NB-Group)<br />
	&emsp;&emsp;-窗口云母(Mica)效果(由 @tjy-gitnub 提供，在设置中启用)<br />
	&emsp;&emsp;-文件资源管理器导航栏(由 @User782Tec 提供)<br />
	&emsp;&emsp;-任务管理器小组件(由 @User782Tec 提供)<br />
	&emsp;&emsp;-全新壁纸(由 @tjy-gitnub 提供)<br />
	&emsp;&emsp;-新增新闻(来自 @for-the-zero)<br />
	&emsp;&emsp;-修复了终端的灾难性bug<br />
	&emsp;&emsp;-优化部分UI<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details>
	<summary><span>v6.0.0</span> 文件系统,窗口大小,天气小组件,任务栏小组件</summary><p>
	
		&emsp;&emsp;-新增文件系统(由 @NB-Group 之后提供)<br />
		&emsp;&emsp;-窗口大小调整功能(由 @User782Tec 提供)<br />
		&emsp;&emsp;-保存桌面布局(由 @User782Tec 提供)<br />
		&emsp;&emsp;-天气小组件(由 @tjy-gitnub 提供)<br />
		&emsp;&emsp;-小组件添加到任务栏(由 @tjy-gitnub 提供,目前仅支持天气小组件)<br />
		&emsp;&emsp;-引入全新提示框(由 @tjy-gitnub 提供)<br />
		&emsp;&emsp;-新的反馈通道<br />
		&emsp;&emsp;-修复了有时开机动画不能正确显示的问题<br />
		&emsp;&emsp;-修复了登录界面不显示的问题<br />
		&emsp;&emsp;-适配新的主题格式，加速预览图显示<br />
		&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details>
	<summary><span>v5.4.2</span> 灾难性程序错误修复</summary><p>
	
		&emsp;&emsp;-修复pwa将主题获取请求拦截的重大灾难性事故<br />
		&emsp;&emsp;-细节优化和修复
	</p>
</p></details>
<details>
	<summary><span>v5.4.1</span> bug修复</summary><p>
	
		&emsp;&emsp;<strong>(更新来自 @tjy-gitnub)</strong><br />
		&emsp;&emsp;-修复pwa将动态请求拦截的灾难性事故<br />
		&emsp;&emsp;-调整大家梦寐以求的开始菜单滚动条<br />
		&emsp;&emsp;-新增开始菜单固定项右键取消固定选项<br />
		&emsp;&emsp;-适配主题格式的一些更改<br />
		&emsp;&emsp;-细节优化和修复
	</p>
</p></details>
<details>
	<summary><span>v5.4.0</span> 新版logo与图标，UI优化，自定主题与winver</summary><p>
	
		&emsp;&emsp;<strong>(更新来自 @tjy-gitnub,其实我想发v6的)</strong><br />
		&emsp;&emsp;-新Logo:新版logo<br />
		&emsp;&emsp;-新图标:重绘替换了win11的图标<br />
		&emsp;&emsp;-自定主题:支持用户更改和上传自己的主题<br />
		&emsp;&emsp;-版本信息:新增winver<br />
		&emsp;&emsp;-新UI:加入win11的悬停立体高亮<br />
		&emsp;&emsp;-启动优化:加快速度，修复本地因获取电量失败无法启动的问题<br />
		&emsp;&emsp;-UI适配:稍稍更改了不适配的UI<br />
		&emsp;&emsp;-Bug修复:右键菜单无法关闭<br />
		&emsp;&emsp;-代码优化:对某些重复代码整理归纳，引入应用的初始化load配置<br />
		&emsp;&emsp;-细节优化和修复<br />
	</p>
</p></details>
<details>
	<summary><span>v5.3.1</span> 问题修复</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @User782Tec</strong><br />
		&emsp;&emsp;-修复了样式不正常的问题<br />
		&emsp;&emsp;-修复了部分情况下Edge浏览器中加载动画圆圈位置不正确的问题<br />
		&emsp;&emsp;-修复了开始菜单无法滚动的问题<br />
		&emsp;&emsp;-修复了控制面板的样式问题<br />
	</p>
</p></details>
<details>
	<summary><span>v5.3.0</span> 触控设备适配、“运行”应用、“白板”应用、控制中心与细节修复</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @NB-Group 和 @User782Tec</strong><br />
		&emsp;&emsp;-新增“运行”应用，可根据输入内容运行命令、打开文件夹或访问网站<br />
		&emsp;&emsp;-新增白板应用，可进行作画并保存<br />
		&emsp;&emsp;-新增控制中心，可调整显示器亮度<br />
		&emsp;&emsp;-新增了右键点击任务栏图标后可以关闭应用的功能<br />
		&emsp;&emsp;-新增了将应用固定到开始菜单的功能<br />
		&emsp;&emsp;-新增了在Edge浏览器中获取网页标题的功能(此项更新由@NB-Group提供后端，@User782Tec完善前端)<br />
		&emsp;&emsp;-适配触控设备在Edge浏览器中进行标签页拖动排序操作<br />
		&emsp;&emsp;-修复了Edge浏览器中标签页拖动排序功能中计算位置不正确等的问题<br />
		&emsp;&emsp;-修复了部分情况下Edge浏览器中加载动画圆圈位置不正确的问题<br />
		&emsp;&emsp;-修复了部分情况下终端不能正常运作的问题(详见由NebulaTechs提出的Issue#87)<br />
		&emsp;&emsp;-修复了部分情况下Edge浏览器中无法正确显示标签页的问题<br />
		&emsp;&emsp;-其他一些更改<br />
		&emsp;&emsp;-格式化部分文档<br />
	</p>
</p></details>
<details>
	<summary><span>v5.2.4</span> Python编辑器</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @NB-Group</strong><br />
		&emsp;&emsp;-新增 Python 编辑器应用，编辑器自带自动补全，代码高亮和运行功能。<br />
	</p>
</p></details>
<details>
	<summary><span>v5.2.3</span> 登录界面优化和Python解释器</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @NB-Group 和 @User782Tec</strong><br />
		&emsp;&emsp;-改进了登录界面，增加锁定壁纸及电源操作<br />
		&emsp;&emsp;-兼容开发环境，在本地开发时不启用缓存和PWA<br />
		&emsp;&emsp;-新增了Python解释器(此项更新由@NB-Group提供灵感与框架，@User782Tec完善)<br />
		&emsp;&emsp;-修复了终端选择不正常的问题<br />
		&emsp;&emsp;-改进了终端，可通过应用标识符来打开应用<br />
		&emsp;&emsp;-格式化部分文档<br />
	</p>
</p></details>
<details>
	<summary><span>v5.2.2</span> 细节优化</summary><p>
	
		&emsp;&emsp;-对相机外观和代码进行了优化<br />
		&emsp;&emsp;-添加登录背景，一些外观优化<br />
		&emsp;&emsp;-开源许可的完善<br />
		&emsp;&emsp;-一些优化和修复<br />
		&emsp;&emsp;-吊一下胃口:v6将使用全新图标(壁纸?)，支持自定义主题 >_-)o
	</p>
</p></details>
<details>
	<summary><span>v5.2.1</span> 登录界面，开机音效和细节优化</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @User782Tec</strong><br />
		&emsp;&emsp;-为网站添加Favicon和标题<br />
		&emsp;&emsp;-Edge浏览器中，超出标签页范围的文字将会被隐藏以避免文字溢出的问题<br />
		&emsp;&emsp;-微调Edge浏览器中加载动画的位置<br />
		&emsp;&emsp;-新增了开机音效和登录界面<br />
		&emsp;&emsp;-格式化部分文档<br />
	</p>
</p></details>
<details>
	<summary><span>v5.2.0</span> 相机应用及细节修复</summary><p>
	
		&emsp;&emsp;<strong>更新来自 @User782Tec</strong><br />
		&emsp;&emsp;-新增“相机”应用，支持拍照功能<br />
		&emsp;&emsp;-优化焦点控制，关闭焦点窗口后，焦点会转移至下面一个窗口，更加符合原生体验<br />
		&emsp;&emsp;-计算器优化，优化符号及输入<br />
		&emsp;&emsp;-修复桌面批量选择框在左上角出现蓝点的问题<br />
		&emsp;&emsp;-修复部分情况下标题栏不显示的问题<br />
		&emsp;&emsp;-优化窗口布局<br />
		&emsp;&emsp;-修正部分非标准CSS特性<br />
		&emsp;&emsp;-格式化部分文档<br />
	</p>
</p></details>
<details>
	<summary><span>v5.1.2</span> 细节修复和更改</summary><p>
	
		&emsp;&emsp;-修复关于加载失效的问题<br />
		&emsp;&emsp;-设置加载圈圈为主题色<br />
		&emsp;&emsp;-添加刷新圈圈动画<br />
		&emsp;&emsp;-优化窗口拖拽代码<br />
		&emsp;&emsp;-其它一些更改<br />
		&emsp;&emsp;-提示:在github项目页的一些<a
			onclick="window.open('https://github.com/tjy-gitnub/win12','_blank');"
			win12_title="https://github.com/tjy-gitnub/win12" class="jump">公告</a>
		</p>
</p></details>
<details>
	<summary><span>v5.1.1</span> 网页加载动画与新增应用</summary><p>
	
		&emsp;&emsp;更新来自 @User782Tec<br />
		&emsp;&emsp;-优化了桌面文件拖拽选择选择<br />
		&emsp;&emsp;-Edge浏览器中网页加载时的标签页动画<br />
		&emsp;&emsp;-增加了点击窗口图标后弹出菜单的功能（部分触控设备不能触发上下文菜单）<br />
		&emsp;&emsp;-增加应用 VSCode 与 哔哩哔哩<br />
		&emsp;&emsp;-其它: 格式化文档和修复
	</p>
</p></details>
<details>
	<summary><span>v5.1.0</span> 标签页拖动排序与云母效果</summary><p>
	
		&emsp;&emsp;-标签页:可拖动标签页排序(仅桌面端)<br />
		&emsp;&emsp;-云母效果(试验):在设置中启用并按"用法"操作<br />
		&emsp;&emsp;-其它:细节优化和修复
	</p>
</p></details>
<details>
	<summary><span>v5.0.2</span> 贡献与Edge标签页</summary><p>
	
		&emsp;&emsp;-贡献:关于应用新增贡献者数据<br />
		&emsp;&emsp;-新标签页:对深色模式的适配，较高安全限制浏览器的适配<br />
		&emsp;&emsp;-标签页:新增标签页关闭动画<br />
		&emsp;&emsp;-兼容性:系统控件的防误触<br />
		&emsp;&emsp;-其它:细节优化和修复
	</p>
</p></details>
<details>
	<summary><span>v5.0.1</span> 漏洞修复</summary><p>
	
		<strong>更新由 @User782Tec 提供</strong><br />
		&emsp;&emsp;-修复了 issues #67中的问题<br />
		&emsp;&emsp;-修复了关闭所有Edge浏览器中的标签页后仍保留Edge浏览器的问题<br />
		&emsp;&emsp;-完善Edge浏览器内输入内容的正则表达式匹配<br />
		&emsp;&emsp;-略微修改Edge浏览器中输入框<br />
		&emsp;&emsp;-新标签页<br />
		&emsp;&emsp;-细节优化和修复
	</p>
</p></details>
<details>
	<summary><span>v5.0.0</span> 新增Edge</summary><p>
	
		<strong>🎉🎉项目在Github上突破✨100✨stars✨啦！🎊🎈</strong><br />
		&emsp;&emsp;-新增:Microsoft Edge应用(可以在Edge应用中提出反馈与建议，谢谢)<br />
		&emsp;&emsp;-新增:桌面拖动选择框(没什么大用)<br />
		&emsp;&emsp;-返璞归真:取消列表悬停效果<br />
		&emsp;&emsp;-丰富:在文件资源管理器中添加了一些文件<br />
		&emsp;&emsp;-精简:减小动画数量和幅度<br />
		&emsp;&emsp;-修复:关于记事本的另存为问题<br />
		&emsp;&emsp;-彩蛋:你发现动态壁纸了吗？<br />
		&emsp;&emsp;-其它:细节优化和修复
	</p>
</p></details>
<details><summary><span>v4.3.0</span> 其实我想发布成5.0版的</summary><p>
	&emsp;&emsp;-搜索菜单重新设计<br />
	&emsp;&emsp;-列表悬停立体效果(全局,深色模式可能会明显一点,灵感源于win11,大家可以帮忙找一个浅色模式的边框颜色)<br />
	&emsp;&emsp;-新增鼠标悬停提示框(全局)<br />
	&emsp;&emsp;-记事本全新设计与功能(向Word努力)<br />
	&emsp;&emsp;-计算器新样式<br />
	&emsp;&emsp;-新增多开亚克力设置<br />
	&emsp;&emsp;-关于应用更新记录动画<br />
	&emsp;&emsp;-文件资源管理器的新动画<br />
	&emsp;&emsp;-桌面图标点击效果<br />
	&emsp;&emsp;-修复设置的小竖条位置问题<br />
	&emsp;&emsp;-新闻<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v4.2.0</span> 外观整改</summary><p>
	&emsp;&emsp;-新的计算器外观<br />
	&emsp;&emsp;-卡片外观优化<br />
	&emsp;&emsp;-设置新增菜单前面的小竖线<br />
	&emsp;&emsp;-右键菜单阴影特效<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v4.1.0</span> 新的设置项，新背景</summary><p>
	&emsp;&emsp;-在设置中添加开关特效的选项<br />
	&emsp;&emsp;-背景添加阴影效果<br />
	&emsp;&emsp;-设置的一些动画效果<br />
	&emsp;&emsp;-一些外观优化<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v4.0.0</span> 记录用户偏好,动画更新,新壁纸,分屏</summary><p>
	&emsp;&emsp;-记录用户偏好设置(更新后不保留) @xqwcom<br />
	&emsp;&emsp;-没错又是动画更新<br />
	&emsp;&emsp;-开始/任务栏/右键菜单/列表/切换等全面动画升级<br />
	&emsp;&emsp;-新壁纸(好看吗?)<br />
	&emsp;&emsp;-拖动窗口至左右边缘分屏<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.9.0</span> 新增主题色设置功能，布局优化</summary><p>
	&emsp;&emsp;<i>更新部分来源 @User782Tec 的pr</i><br />
	&emsp;&emsp;-新增系统主题色设置功能<br />
	&emsp;&emsp;-记事本字体选择布局优化<br />
	&emsp;&emsp;-"关于"应用的改进<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.8.0</span> 记事本字体，窗口焦点逻辑改善</summary><p>
	&emsp;&emsp;<i>更新主要来源 @User782Tec 的pr</i><br />
	&emsp;&emsp;-详见<a href="https://github.com/tjy-gitnub/win12/pull/50">pull</a>中的详细<br />
	&emsp;&emsp;-窗口焦点逻辑改善<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.7.0</span> 窗口最大化完善,让颜色更"喜庆"一点</summary><p>
	&emsp;&emsp;<i>更新部分来源 @User782Tec 的pr</i><br />
	&emsp;&emsp;-增加最大化窗口下拖还原功能<br />
	&emsp;&emsp;-最大化窗口还原后保留之前的位置<br />
	&emsp;&emsp;-让系统的色调更"喜庆"一点<br />
	&emsp;&emsp;-增加 @iamkezo1 提供的新闻<br />
	&emsp;&emsp;-压缩背景图片加速<br />
	&emsp;&emsp;-禁用缩放<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.6.1</span> Windows12新年特别版</summary><p>
	&emsp;&emsp;-增加主题色控制功能<br />
	&emsp;&emsp;-测试新的更新机制<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.6.0</span> 更新机制改进，新增office和终端</summary><p>
	&emsp;&emsp;-使用新的更新机制，更新速度提升<br />
	&emsp;&emsp;-新增终端应用<br />
	&emsp;&emsp;-新增Office<br />
	&emsp;&emsp;-修改关机后黑屏而不是白屏<br />
	&emsp;&emsp;-更新记录仅显示最近的，优化速度<br />
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.5.0</span> 窗口层级和焦点控制</summary><p>
	&emsp;&emsp;-增加了窗口层级的控制<br>
	&emsp;&emsp;-非焦点窗口关闭模糊效果以提速<br>
	&emsp;&emsp;-新增 @Planet-xu 提供的新闻<br>
	&emsp;&emsp;-修复了下拉菜单停留的问题 @Samtjs<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.4.1</span> 移动端适配</summary><p>
	&emsp;&emsp;-适配了移动端的双(改成单击)<br>
	&emsp;&emsp;-适配了移动端的拖动窗口<br>
	&emsp;&emsp;-优化任务栏按钮点击效果<br>
	&emsp;&emsp;-更改右键菜单样式<br>
	&emsp;&emsp;-下拉菜单悬停展开<br>
	&emsp;&emsp;-细节优化和修复</p>
	<i>感谢 @User782Tec</i>
</p></details>
<details><summary><span>v3.4.0</span> 动画优化</summary><p>
	&emsp;&emsp;-别问我为什么动画占一个新版<br>
	&emsp;&emsp;-基本上所有动画都进行了更新优化<br>
	&emsp;&emsp;-修复了上个版本没删完的标题栏问题<br>
	&emsp;&emsp;-修改了开始菜单的图标<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.3.0</span> 增加计算器小组件</summary><p>
	&emsp;&emsp;-增加了计算器小组件<br>
	&emsp;&emsp;-修复了小组件新闻图片无法加载的问题<br>
	&emsp;&emsp;-移除了上一个版本的新窗口标题栏<br>
	&emsp;&emsp;-在一些图标按钮上悬停鼠标显示提示<br>
	&emsp;&emsp;-去除了所有链接的左下角链接提示<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.2.0</span> 增加小组件菜单</summary><p>
	&emsp;&emsp;-增加了小组件(资讯和兴趣)菜单<br>
	&emsp;&emsp;-小组件菜单中的新闻内容<br>
	&emsp;&emsp;-小组件按钮动画效果<br>
	&emsp;&emsp;-应用窗口标题栏样式新增<br>
	&emsp;&emsp;-对计算器的计算代码的精简<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.1.0</span> 增加文件浏览功能，一些新功能</summary><p>
	&emsp;&emsp;-文件资源管理器浏览功能<br>
	&emsp;&emsp;-对PWA更新的优化<br>
	&emsp;&emsp;-文件资源管理器右键菜单<br>
	&emsp;&emsp;-增加应用启动的加载动画<br>
	&emsp;&emsp;-增加应用启动初始化<br>
	&emsp;&emsp;-记事本新增"编辑"菜单<br>
	&emsp;&emsp;-下拉菜单整体优化<br>
	&emsp;&emsp;-右键菜单优化<br>
	&emsp;&emsp;-在输入框中允许浏览器的右键菜单<br>
	&emsp;&emsp;-双击窗口标题栏最大化<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v3.0.0</span> 桌面图标、记事本、可安装到电脑</summary><p>
	&emsp;&emsp;-可以在Edge、Chrome等主流浏览器中将Win12安装为pwa应用¹<br>
	&emsp;&emsp;-新增桌面图标，并增加桌面右键菜单"刷新"功能<br>
	&emsp;&emsp;-可以将开始菜单应用右键添加到桌面<br>
	&emsp;&emsp;-新增"记事本"应用<br>
	&emsp;&emsp;-新的深色模式切换按钮<br>
	&emsp;&emsp;-改进了窗口的外观<br>
	&emsp;&emsp;-细节优化和修复<br>
	<i>1: 正常会有安装的提示，会在链接栏右上角，安装后可离线使用，有网会自动更新。感谢 @dzc120223 的建议</i>
</p>
</p></details>
<details><summary><span>v2.3.1</span> bug修复、外观优化</summary><p>
	&emsp;&emsp;-修复了拖动窗口图标触发拖拽事件的错误<br>
	&emsp;&emsp;-修复了其它拖拽图片的错误<br>
	&emsp;&emsp;-右键菜单的外观优化<br>
	&emsp;&emsp;-深色模式的可读性优化<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v2.3.0</span> 开始外观优化、日历菜单、任务栏图标动画</summary><p>
	&emsp;&emsp;-开始菜单外观优化、更加松散<br>
	&emsp;&emsp;-新增任务栏中的日历菜单<br>
	&emsp;&emsp;-任务栏图标添加动画效果<br>
	&emsp;&emsp;-窗口最小化动画优化<br>
	&emsp;&emsp;-右键菜单的完善<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v2.2.0</span> 开始、搜索按钮动画，右键菜单优化，新列表点击效果</summary><p>
	&emsp;&emsp;-开始、搜索按钮新增点击动画<br>
	&emsp;&emsp;-优化右键菜单<br>
	&emsp;&emsp;-桌面右键菜单新增<br>
	&emsp;&emsp;-新的列表点击效果<br>
	&emsp;&emsp;-任务栏动画效果优化<br>
	&emsp;&emsp;-细节优化和修复</p>
</p></details>
<details><summary><span>v2.1.1</span> 更多右键菜单，win10主题列表悬停效果</summary><p>
	&emsp;&emsp;-在开始、任务栏、更新消息支持更多右键菜单<br>
	&emsp;&emsp;-右键菜单超出界面的优化<br>
	&emsp;&emsp;-列表win10悬停效果<br>
	&emsp;&emsp;-点击或右击消息可在窗口中查看详细<br>
	&emsp;&emsp;-修复了时间无法显示星期日的问题<br>
	&emsp;&emsp;-细节优化</p>
</p></details>
<details><summary><span>v2.1.0</span> 右键菜单，细节优化更改</summary><p>
	&emsp;&emsp;-在窗口标题、桌面、开始菜单支持右键菜单<br>
	&emsp;&emsp;-在其它元素上禁用右键菜单<br>
	&emsp;&emsp;-列表点击根据鼠标位置偏移<br>
	&emsp;&emsp;-新增更新消息图标<br>
	&emsp;&emsp;-修复了按钮点击缩小效果不正常显示的问题<br>
	&emsp;&emsp;-修复了更新消息在开机前显示的问题<br>
	&emsp;&emsp;-细节优化</p>
</p></details>
<details><summary><span>v2.0.0</span> 深色主题、更新提示、新点击效果、拖至顶部最大化</summary><p>
	&emsp;&emsp;-支持深色模式<br>
	&emsp;&emsp;-在每次更新后显示更新提示<br>
	&emsp;&emsp;-去除原 win10 主题点击、悬停动画<br>
	&emsp;&emsp;-新的列表项点击动画<br>
	&emsp;&emsp;-将窗口拖至顶部最大化<br>
	&emsp;&emsp;-优化开始菜单固定项点击效果</p>
</p></details>
<details><summary><span>v1.1.4</span> 动画优化、显示时间</summary><p>
	&emsp;&emsp;-优化窗口最大最小化动、开始、搜索等动画<br>
	&emsp;&emsp;-计算器外观优化<br>
	&emsp;&emsp;-"准备"深色主题😏<br>
	&emsp;&emsp;-在开始菜单中显示当前的时间<br>
	&emsp;&emsp;-一些细节优化</p>
</p></details>
<details><summary><span>v1.1.3</span> 列表 悬停、点击 效果</summary><p>
	&emsp;&emsp;-在开始菜单、设置、关于、文件资源管理器、计算器中使用新的列表悬停、点击效果<br>
	&emsp;&emsp;-在任务栏、关于中使用主题色(蓝紫渐变)<br>
	&emsp;&emsp;-任务栏应用圆角优化<br>
	&emsp;&emsp;-增加任务栏右下角托盘(未完全实现)<br>
	&emsp;&emsp;-使启动时不显示鼠标，更加逼真</p>
</p></details>
<details><summary><span>v1.1.2</span> 外观优化</summary><p>
	&emsp;&emsp;-透明效果改善<br>
	&emsp;&emsp;-在所有应用中使用主题色(蓝紫渐变)<br>
	&emsp;&emsp;-Dock(任务)栏圆角优化<br>
	&emsp;&emsp;-在设置中使用新的头像<br>
	&emsp;&emsp;-加速窗口动画，更加贴近原生</p>
</p></details>
<details><summary><span>v1.1.1</span> 外观优化，修复了一些 Bug</summary><p>
	&emsp;&emsp;-外观优化，阴影增加<br>
	&emsp;&emsp;-修复了开始、搜索菜单关闭时高度会闪一下的问题<br>
	&emsp;&emsp;-开始菜单用户头像美化<br>
	&emsp;&emsp;-修复了"关于"应用切换标签时文字改变导致动画流畅的问题<br>
	&emsp;&emsp;-使页面不能滚动，更加逼真</p>
</p></details>
<details><summary><span>v1.1.0</span> 增加最小化功能,图标美化</summary><p>
	&emsp;&emsp;-新增最小化窗口功能<br>
	&emsp;&emsp;-图标部分美化<br>
	&emsp;&emsp;-开始、搜索按钮点击动画美化<br>
	&emsp;&emsp;-修复计算器 "𝑥²" 键无效的问题<br>
	&emsp;&emsp;-修复窗口打开并最大化后最小化不正常的问题<br>
	&emsp;&emsp;-修复多次打开应用任务栏显示错误的问题<br>
	&emsp;&emsp;-简化js关于拖动窗口的代码</p>
</p></details>
<details><summary><span>v1.0.1</span> Bug和一些错别字</summary><p>
	&emsp;&emsp;上传了才发现有 Bug 😅</p>
</p></details>
<details><summary><span>v1.0.0</span> 计算器功能增加，优化体验与开始菜单，新增关于应用</summary><p>
	&emsp;&emsp;-使开始菜单和搜索窗口在高度不足的页面中更好地显示，页面高度过小也可能显示不全，还是建议使用电脑<br>
	&emsp;&emsp;-计算器增加平方与开方功能<br>
	&emsp;&emsp;-开始菜单中不可用应用用灰色显示，更加简洁方便<br>
	&emsp;&emsp;-新增 "关于Win12网页版" 应用，包含关于本项目的说明和历史更新记录<br>
	&emsp;&emsp;-优化开始菜单和搜索窗口的显示动画<br>
	&emsp;&emsp;-优化电脑端按钮的体验</p>
</p></details>
