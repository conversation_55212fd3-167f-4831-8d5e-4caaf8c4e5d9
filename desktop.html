<!DOCTYPE html>
<html lang="en" style="height: 100%;">

<!--------禁止格式化本文档!  -------->
<!-----No Formatting the Code! ---->

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="format-detection" content="telephone=no" />

	<!-- Windows 12 网页版是一个开放源项目,
	希望让用户在网络上预先体验 Windows 12,
	内容可能与 Windows 12 正式版本不一致。
	使用标准网络技术，例如 HTML、CSS 和 JavaScript，
	此项目绝不附属于微软，且不应与微软操作系统或产品混淆，
	这也不是 Windows365 cloud PC
	本项目中微软、Windows 和其他示范产品是微软公司的商标 -->
	<link rel="stylesheet" href="./desktop.css">
	<link rel="stylesheet" href="bootstrap-icons.css">
	<!-- Apps style -->
	<link rel="stylesheet" href="apps/style/setting.css">
	<link rel="stylesheet" href="apps/style/explorer.css">
	<link rel="stylesheet" href="apps/style/calc.css">
	<link rel="stylesheet" href="apps/style/about.css">
	<link rel="stylesheet" href="apps/style/notepad.css">
	<link rel="stylesheet" href="apps/style/terminal.css">
	<link rel="stylesheet" href="apps/style/edge.css">
	<link rel="stylesheet" href="apps/style/camera.css">
	<link rel="stylesheet" href="apps/style/pythonEditor.css">
	<link rel="stylesheet" href="apps/style/run.css">
	<link rel="stylesheet" href="apps/style/whiteboard.css">
	<link rel="stylesheet" href="apps/style/defender.css">
	<link rel="stylesheet" href="apps/style/taskmgr.css">
	<link rel="stylesheet" href="apps/style/msstore.css">
	<!-- 掌声欢迎 Copilot 的 css 引入(bs -->
	<link rel="stylesheet" href="apps/style/copilot.css">
	<!--  掌声欢迎 语音识别输入法 的 css 引入(bs -->
	<link rel="stylesheet" href="apps/style/recognition.css">
	
	<link rel="manifest" href="pwa/manifest.json">
	<link rel="shortcut icon" href="./pwa/logo.png" type="image/x-icon">
	<link rel="stylesheet" href="apps/style/login.css">
	<!-- 模块 -->
	<link rel="stylesheet" href="module/widget.css">
	<link rel="stylesheet" href="module/tab.css">

	
	<!-- 风水宝地，不可移动 -->
	<script src="./scripts/jq.min.js"></script>
	<script src="./scripts/jquery.i18n.properties.js"></script>

	<base target="_blank">

	<title>Windows 12 网页版</title>
	<meta name="description" content="Windows 12 网页版是一个在线体验 Windows 12 操作系统的开源项目, 使用 HTML、CSS 和 JavaScript 模拟 Windows 12 操作系统的界面与交互。">
<!-- 	<meta name="keywords" content="Windows 12, windows 12, Windows12, windows12, Windows 12 网页版, Windows 12网页版, Windows12网页版, windows12网页版, Win12, win12,Win12网页版, win12网页版, 网页版"> -->
<!-- 	真正的SEO强者，是不会用keywords的 -->
</head>


<!-- 编辑前务必认真阅读，有不合规范之pr，视情况或要求整改或不予合并 -->
<!-- 开发须知：

旨为统一代码风格，增加其可读性，也为他人开发便利，以下内容所有开发者、贡献者务必严格遵守，感谢配合！

对于HTML文件的规定
1. 绝对严格禁止格式化本 html 文档，否则坚决拒绝合并
2. 对于id属性，除必须外一般不使用id属性，尽量以class替代。若必须使用，切记：
	1. 若非 body 下直接的子元素，原则上不设置 id。若确需，参见下方条目 3
	2. 所取 id 必须有意义，且不可有与其他 id 重复者
	3. 对于非 body>* 元素，坚决不得以单个单词作为 id。请按照 “父元素标志词-(...)-id名称” 进行命名。如：taskmgr-search, setting-search等
3. 对于 class 属性：
	1. 仅对必要的元素分配 class
	2. 所取的名字必须要有意义
	3. 使用css选择器时，确保所选元素准确定位且无其他匹配，以 id 选择器开头来选择为佳
4. 对于压行，可在保证可读性前提下自由处置，参考规范：
	1. 对于文件中的 svg 图像，压为一行
	2. 对于连续3个以上的连续结束标签，视情况压为一行
	3. 对于较短的多个并列雷同的项，每项视情况压为一行，如list中的内容
	4. 对于不更改、无具体意义的内容，可视情况处置

一些全局性可用的内容，供使用和统一风格：
.a 可点击元素，可用于各种元素。用于 <a> 可去除下划线、颜色等。
	与 .jump 合用于设置了 href 属性的 <a> 链接
.button 经典按钮，常与 .a 合用
list 列表>
	<a> 列表项，常与.a合用
	.text 文本内容，不可点击
.buttons 按钮组>
	.submit 确认，常与.a合用
	.cancel 取消，常与.a合用
.act 经典点击动画
.input 输入框
其他自行研究

有以下 js 函数可用：
setData(key, value) 本地存储信息。注意管理，谨慎使用
localStorage.getItem(key) 与前者配合使用，以读取存储信息

stop(event) 停止事件向父级传递事件（停止冒泡），event变量照抄。可使祖先元素接受不到发生的对应事件。

showcm(event, id, arg) 显示右键菜单，使用方法：oncontextmenu="return showcm(event,'标识',arg);"，
	参考 desktop.js 中 cms 中的写法，需提前在 cms 中定义提示内容及其标识符。showcm 带有 stop(event), 无需另加
openapp(name) 打开窗口，用法：openapp('程序（窗口）名称')，
	程序（窗口）名称就是想要显示的窗口.window的另一个类名，须设置 apps[程序（窗口）名称]对象和init方法
shownotice(name) 打开提示窗口，用法：shownotice('提示内容标识符')，
	参考 desktop.js 中 nts 中的写法，需提前在 nts 对象中定义提示内容及其标识符
closenotice() 与前者配合，关闭提示窗口
showwin(name) 显示窗口，除特殊用途外不建议使用，用法：showwin('窗口名称')，注意：窗口名称就是想要显示的窗口.window的另一个类名
hidewin(name) 关闭窗口，用法：hidewin('程序（窗口）名称')，注意：程序（窗口）名称就是想要显示的窗口.window的另一个类名
toggletheme() 切换主题

关于 App 添加：

壹 常规 app
	1. 界面与(伪)后端的开发，js 在 module/apps.js 中，css 和资源文件在 apps/style 和 apps/icons 中
	2. 绘制图标，存储于 icon 中，注意后缀名，参考 desktop.js 中 @icon 的说明
	3. 在开始菜单 (@s-m-l>list) 中添加你的应用

贰 webapp，即使用 iframe 嵌套网页的应用：
	1. 确保目标网页不阻止跨域嵌套，允许 iframe 嵌套
	2. html 内容参考 @.window.bilibili.webapp
	3. 在 module/apps.js 中参考 @apps[bilibili]
	4. 在 module/apps.js 中 @apps[webapps] 里补充 appid

欢迎补充~
-->
<body>
	<audio src="media/startup.mp3" id="startup-music"></audio>
	<div id="loadback" style="background-color:#000;width:100%;height:100%;z-index:105;position:absolute;transition:200ms;cursor: none;/*加载界面隐藏鼠标指针*/">
		<style>
			#loadback.hide {
				opacity: 0;
			}
			loading>svg>circle:last-child {
				stroke: #fff;
				fill: none;
				stroke-width: 2px;
				stroke-linecap: round;
				animation: spin-infinite 2.5s linear 0s infinite normal none running;
				transform-origin: 50% 50%;
				transition: all .2s ease-in-out 0s;
			}
			loading>svg {
				background-color: #00000000;
				border-radius: 50%;
			}
			@keyframes spin-infinite {
				0% {
					stroke-dasharray: .01px, 43.97px;
					transform: rotate(0deg)
				}
				50% {
					stroke-dasharray: 21.99px, 21.99px;
					transform: rotate(450deg)
				}
				to {
					stroke-dasharray: .01px, 43.97px;
					transform: rotate(3turn)
				}
			}
		</style>
		<svg id="loadbacksvg" viewBox="21,18,320,315" style="transition: 1s;opacity: 0;position: absolute;left: calc(50% - 125px);top:20%;" width="250" height="250" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.81006 5.77778"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.77778 5.77778"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="7.36593 7.36776"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.521569" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="6 6"/></filter><clipPath id="clip4"><rect x="468" y="169" width="362" height="356"/></clipPath><clipPath id="clip5"><rect x="-6.53632" y="-6.5" width="101.061" height="108"/></clipPath><clipPath id="clip6"><rect x="0" y="0" width="90" height="95"/></clipPath><linearGradient x1="525.053" y1="357.279" x2="645.947" y2="458.721" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill7"><stop offset="0" stop-color="#775CFE"/><stop offset="0.18" stop-color="#775CFE"/><stop offset="0.83" stop-color="#2473DC"/><stop offset="1" stop-color="#2473DC"/></linearGradient><clipPath id="clip8"><rect x="-6.5" y="-6.5" width="125" height="115"/></clipPath><clipPath id="clip9"><rect x="0" y="0" width="112" height="105"/></clipPath><linearGradient x1="511.623" y1="213.086" x2="642.377" y2="368.914" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill10"><stop offset="0" stop-color="#C45DD5"/><stop offset="0.18" stop-color="#C45DD5"/><stop offset="0.69" stop-color="#A266E4"/><stop offset="1" stop-color="#A266E4"/></linearGradient><clipPath id="clip11"><rect x="-7.53336" y="-7.53522" width="128.067" height="124.08"/></clipPath><clipPath id="clip12"><rect x="0" y="0" width="113" height="107"/></clipPath><linearGradient x1="617.245" y1="376.897" x2="771.755" y2="466.103" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill13"><stop offset="0" stop-color="#61A2F9"/><stop offset="0.18" stop-color="#61A2F9"/><stop offset="0.91" stop-color="#3159D7"/><stop offset="1" stop-color="#3159D7"/></linearGradient><clipPath id="clip14"><rect x="-6.5" y="-6.5" width="130" height="137"/></clipPath><clipPath id="clip15"><rect x="0" y="0" width="118" height="124"/></clipPath><linearGradient x1="643.458" y1="199.26" x2="791.542" y2="375.74" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill16"><stop offset="0" stop-color="#8A66FE"/><stop offset="0.19" stop-color="#8A66FE"/><stop offset="1" stop-color="#2473DC"/></linearGradient></defs><g clip-path="url(#clip4)" transform="translate(-468 -169)"><g clip-path="url(#clip5)" filter="url(#fx0)" transform="matrix(1.98889 0 0 2 499 310)"><g clip-path="url(#clip6)" transform="translate(2.84217e-14 0)"><path d="M71.8132 77.0858 26.981 77.0858C22.0288 77.0858 18.0143 73.1038 18.0143 68.1917L18.0143 19.9983C27.6747 18.8595 31.5867 17.632 40.5532 18.2527 49.5197 18.8734 62.1334 21.5426 71.8132 23.7225L71.8132 77.0858Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M639 467 549.834 467C539.984 467 532 459.036 532 449.212L532 352.825C551.213 350.547 558.994 348.093 576.827 349.334 594.661 350.575 619.748 355.914 639 360.273L639 467Z" fill="url(#fill7)" fill-rule="evenodd"/><g clip-path="url(#clip8)" filter="url(#fx1)" transform="matrix(2 0 0 2 468 189)"><g clip-path="url(#clip9)"><path d="M17.9142 86.9142 17.9142 29.4144C17.9142 23.063 23.0408 17.9142 29.3649 17.9142L87.6795 17.9142C91.3242 35.2574 94.0833 41.1006 93.9062 52.6006 93.729 64.1006 89.0466 75.4763 86.6168 86.9142 70.0706 84.6993 65.4194 82.6717 53.5245 82.4844 41.5652 82.296 29.7843 85.4376 17.9142 86.9142Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M501 360 501 245C501 232.298 511.253 222 523.901 222L640.531 222C647.82 256.686 653.338 268.373 652.984 291.373 652.63 314.373 643.265 337.124 638.405 360 605.313 355.57 596.01 351.515 572.221 351.14 548.302 350.764 524.74 357.047 501 360Z" fill="url(#fill10)" fill-rule="evenodd"/><g clip-path="url(#clip11)" filter="url(#fx2)" transform="matrix(1.99115 0 0 1.99065 579 312)"><g clip-path="url(#clip12)"><path d="M90.4862 22.6918 90.4862 74.1822C90.4862 79.8698 85.8764 84.4806 80.1899 84.4806L28.7099 84.4806C25.6989 69.2628 22.7773 68.0805 22.6877 54.0451 22.5938 39.3223 26.7026 33.1429 28.7099 22.6918L90.4862 22.6918Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M762 360 762 462.5C762 473.822 752.821 483 741.499 483L638.994 483C632.999 452.707 627.181 450.353 627.003 422.414 626.816 393.105 634.997 380.805 638.994 360L762 360Z" fill="url(#fill13)" fill-rule="evenodd"/><g clip-path="url(#clip14)" filter="url(#fx3)" transform="matrix(2 0 0 2 594 169)"><g clip-path="url(#clip15)"><path d="M18.7308 18.4142 86.1097 18.4142C93.5523 18.4142 99.5858 24.4663 99.5858 31.932L99.5858 99.5195C79.053 102.327 71.9105 106.654 58.5202 105.134L18.7308 99.5195C21.1161 80.9213 23.4541 75.8406 23.5014 62.3231 23.5527 47.6868 20.321 33.0505 18.7308 18.4142Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M639 203 769.833 203C784.285 203 796 214.752 796 229.248L796 360.486C756.13 365.937 742.262 374.34 716.261 371.389L639 360.486C643.632 324.373 648.171 314.508 648.263 288.26 648.363 259.84 642.088 231.42 639 203Z" fill="url(#fill16)" fill-rule="evenodd"/></g></svg>
		<loading id="loadbackloading" style="transition:1s;opacity:0;">
			<svg style="position:absolute;left:calc(50% - 25px);top:72%;" width="50px" height="50px" viewBox="0 0 16 16">
				<circle cx="8px" cy="8px" r="7px"></circle>
			</svg>
			<p id="loadbackupdate" style="position:absolute;top:calc(72% + 55px);left:0;width:100%;text-align:center;color:#fff;font-size: 25px;display:none;" data-i18n="updating">正在更新</p>
		</loading>
		<script>
			setTimeout(() => {
				$('#loadbacksvg')[0].style.opacity = 1;
				$('#loadbackloading')[0].style.opacity = 1;
			}, 100);
		</script>
	</div>
	<div class="container" id="orientation-warning">
        <div class="image-container">
            <img src="icon/ls.svg" alt="半透明图标" class="svg translucent">
            <img src="icon/ls.svg" alt="旋转图标" class="svg rotating">
        </div>
        <div data-i18n="cover-hp">横屏以获得最佳体验</div>
    </div>
	<div id="loginback">
		<div class="user"></div>
		<div class="name">Administrator</div>
		
		<div id="login" onclick="$(this).css('opacity','0');$('#login-welc').css('opacity','1');
			setTimeout(() => {
				$('#loginback').addClass('close');
				setTimeout(() => {
					$('#loginback').css('opacity', '0');
				}, 500);
				setTimeout(() => {
					$('#loginback').css('display', 'none');
				}, 2000);
				if(use_music){
					document.querySelector('audio#startup-music').play();
				}
			}, 2000);" data-i18n="login">
			登录
		</div>
		<div id="login-welc" style="opacity: 0;">
			<loading>
				<svg width="50px" height="50px" viewBox="0 0 16 16">
					<circle cx="8px" cy="8px" r="6px"></circle>
				</svg>
			</loading>
			<p style="font-size: 22px;
            color: #fff;
			margin-top: 5px;
            text-align: center;" data-i18n="welcome">欢迎</p>
		</div>
		<list class="langselect new">
			<span class="text" style="font-size: 22px;"><i class="bi bi-globe2"></i></span>
			<a class="a def" onclick="
				if(navigator.language in langc)
					localStorage.setItem('lang',langc[navigator.language]);
				else
					localStorage.setItem('lang','en-US');
				window.location.href='';">Default language of the browser</a>
			<a class="a zh-CN" onclick="localStorage.setItem('lang','zh-CN');window.location.href='';">简体中文</a>
			<a class="a zh-TW" onclick="localStorage.setItem('lang','zh-TW');window.location.href='';">繁體中文</a>
			<a class="a en" onclick="localStorage.setItem('lang','en');window.location.href='';">English</a>
		</list>
		<a class="power" onclick="$(this).addClass('show')">
			<!-- <i class="bi bi-translate" onclick="
				if(localStorage.getItem('lang')&&localStorage.getItem('lang')=='en'){
					setData('lang','zh_cn');
				}else{
					setData('lang','en');
				}
				window.location.href='';
				" win12_title="Toggle language"></i> -->
			<i class="bi bi-power" onclick="setTimeout(() => {window.location='shutdown.html';}, 200);" win12_title="关机"></i>
			<i class="bi bi-arrow-counterclockwise" onclick="setTimeout(() => {window.location='reload.html';}, 200);" win12_title="重启"></i>
		</a>
	</div>
	<!-- 下方图片用于拖慢加载速度，测试 loadback -->
	<!-- <img src="https://bing.com/th?id=OHR.Trossachs_ZH-CN9299955040_UHD.jpg&qlt=100" alt=""> -->

	<div id="voiceBall" class="draggable no-drag" style="top: 80%;left: 80%;">
        <div class="rainbow-container-main">
            <div class="rainbow-container">
                <div class="green"></div>
                <div class="pink"></div>
                <div class="blue"></div>
            </div>
        </div>
    </div>


	<div id="start-menu">
		<!-- 开始菜单 -->
		<div id="s-m-l">
			<div id="s-m-user">
				<svg viewBox="0,0,257,344" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="user-clip0"><rect x="382" y="195" width="257" height="344" /></clipPath><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill1"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill2"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient></defs><g clip-path="url(#user-clip0)" transform="translate(-382 -195)"><path d="M637.755 433.872C642.215 515.221 579.577 537.983 508.011 537.983 436.444 537.983 376.676 507.833 383.513 437.11 383.109 425.234 389.59 414.133 398.634 409.891 413.82 402.768 444.753 402.936 507.484 402.997 570.214 403.058 609.164 402.279 621.521 407.947 633.878 413.614 638.011 424.609 637.755 433.872Z" fill="url(#user-fill1)" fill-rule="evenodd" /><path d="M422 285C422 235.847 461.623 196 510.5 196 559.377 196 599 235.847 599 285 599 334.153 559.377 374 510.5 374 461.623 374 422 334.153 422 285Z" fill="url(#user-fill2)" fill-rule="evenodd" /></g></svg>
			</div>
			<p style="width: 100%;text-align: center;margin: -50px 0 20px 0;font-size: 30px;">Administrator</p>
			<input type="text" class="input" win12_title="搜索" placeholder="在这里输入你要搜索的内容" onfocus="hide_startmenu();
				$('#search-btn').addClass('show');
				$('#search-win').addClass('show-begin');
				setTimeout(() => {
					$('#search-win').addClass('show');
				}, 0);
				$('#search-input').focus();" data-i18n-attr="placeholder" data-i18n-key="sch-ph">
			<input-before class="bi bi-search"></input-before>
			<list>
				<p class="text" data-i18n="stmenu-avlb">可用</p>
				<a oncontextmenu="return showcm(event, 'smlapp', ['setting', '设置'])" onclick="openapp('setting');hide_startmenu();">
					<img src="icon/setting.svg">
					<p data-i18n="setting.name">设置</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['explorer', '文件资源管理器'])" onclick="openapp('explorer');hide_startmenu();">
					<img src="icon/explorer.svg">
					<p data-i18n="explorer.name">文件资源管理器</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['calc', '计算器'])" onclick="openapp('calc');hide_startmenu();">
					<img src="icon/calc.svg">
					<p data-i18n="calc.name">计算器</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['run', '运行'])" onclick="openapp('run');hide_startmenu();">
					<img src="icon/run.svg">
					<p data-i18n="run.name">运行</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['about', '关于 Win12 网页版'])" onclick="openapp('about');hide_startmenu();">
					<img src="icon/about.svg">
					<p data-i18n="about.name">关于 Win12 网页版</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['taskmgr', '任务管理器'])" onclick="openapp('taskmgr');hide_startmenu();">
					<img src="icon/taskmgr.png">
					<p data-i18n="taskmgr.name">任务管理器</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['notepad', '记事本'])" onclick="openapp('notepad');hide_startmenu();">
					<img src="icon/notepad.svg">
					<p data-i18n="notepad.name">记事本</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['copilot', 'Windows 12 Copilot'])" onclick="openapp('copilot');hide_startmenu()">
					<img src="icon/copilot.svg">
					<p>Windows 12 Copilot</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['minesweeper', 'Windows 12 minesweeper'])" onclick="openapp('minesweeper');hide_startmenu()">
					 <img src="icon/minesweeper.svg">
				  <p>Windows 12 扫雷</p>
			    </a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['edge', 'Microsoft Edge'])" onclick="openapp('edge');hide_startmenu();">
					<img src="icon/edge.svg">
					<p>Microsoft Edge</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['msstore', 'Microsoft Store'])" onclick="openapp('msstore');hide_startmenu();">
					<img src="icon/msstore.svg">
					<p>Microsoft Store</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['camera', '相机'])" onclick="apps.camera.notice();hide_startmenu()">
					<img src="icon/camera.svg">
					<p data-i18n="camera.name">相机</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['pythonEditor', 'Python Editor'])" onclick="openapp('pythonEditor');hide_startmenu()">
					<img src="icon/pythonEditor.svg">
					<p>Python Editor</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['python', 'Python 3.10.2'])" onclick="openapp('python');hide_startmenu()">
					<img src="icon/python.svg">
					<p>Python 3.10.2</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['terminal', '终端'])" onclick="openapp('terminal');hide_startmenu();">
					<img src="icon/terminal.svg">
					<p data-i18n="terminal.name">终端</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['whiteboard', 'Whiteboard'])" onclick="openapp('whiteboard');hide_startmenu();">
					<img src="icon/whiteboard.svg">
					<p>Whiteboard</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['defender', 'Windows 安全中心'])" onclick="openapp('defender');hide_startmenu();">
					<img src="icon/defender.svg">
					<p data-i18n="defender.name">Windows 安全中心</p>
				</a>
				<p class="text" data-i18n="stmenu-webapp">Web 应用</p>
				<a oncontextmenu="return showcm(event, 'smlapp', ['bilibili', '哔哩哔哩'])" onclick="openapp('bilibili');hide_startmenu();">
					<img src="icon/bilibili.png">
					<p data-i18n="bilibili.name">哔哩哔哩</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['vscode', 'Visual Studio Code'])" onclick="openapp('vscode');hide_startmenu();">
					<img src="icon/vscode.png">
					<p>Visual Studio Code</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['wsa', '适用于 Android™️ 的 Windows 子系统'])" onclick="openapp('wsa');hide_startmenu();">
					<img src="icon/wsa.png">
					<p data-i18n="wsa.name">适用于 Android™ 的 Windows 子系统</p>
				</a>
				<a oncontextmenu="return showcm(event, 'smlapp', ['windows12', 'Windows 12'])" onclick="openapp('windows12');hide_startmenu();">
					<img src="icon/logo.svg">
					<p>Windows 12</p>
				</a>
			</list>
		</div>
		<div id="s-m-r">
			<div class="row1">
				<div class="folder">
					<a class="a sm-app enable"
						onclick="openapp('explorer');apps.explorer.goto('C:/用户/Administrator/文档');hide_startmenu();">
						<img src="icon/folder/docs.svg">
						<p data-i18n="folder.doc">文档</p>
					</a>
					<a class="a sm-app enable"
						onclick="openapp('explorer');apps.explorer.goto('C:/用户/Administrator/图片');hide_startmenu();">
						<img src="icon/folder/pics.svg">
						<p data-i18n="folder.pic">图片</p>
					</a>
					<a class="a sm-app enable"
						onclick="openapp('explorer');apps.explorer.goto('C:/用户/Administrator/音乐');hide_startmenu();">
						<img src="icon/folder/music.svg">
						<p data-i18n="folder.mus">音乐</p>
					</a>
				</div>
				<div class="tool">
					<p style="font-size: 40px;margin-bottom: -10px;" class="time">12:00</p>
					<p class="date">星期一, 2368年13月34日</p>
					<div class="pw">
						<a class="a btn btn-icon big" win12_title="切换全屏" onclick="
							if($('#start-menu').hasClass('max')){
								$('#start-menu>#s-m-r>.row1>.tool>.pw>.btn-icon.big').html('<i class=\'bi bi-arrows-angle-expand\'></i>');
								$('#start-menu').removeClass('max');
							}else{
								$('#start-menu>#s-m-r>.row1>.tool>.pw>.btn-icon.big').html('<i class=\'bi bi-arrows-angle-contract\'></i>');
								$('#start-menu').addClass('max');
							}
							stop(event)
						">
							<i class="bi bi-arrows-angle-expand"></i>
						</a>
						<a class="a btn btn-icon power" win12_title="电源" onclick="$(this).toggleClass('show');">
							<span class="bi bi-power"></span>
							<i class="bi bi-power" onclick="$('#start-menu').removeClass('show');
							setTimeout(() => {window.location='shutdown.html';},200);" win12_title="关机"></i>
							<i class="bi bi-arrow-counterclockwise" onclick="$('#start-menu').removeClass('show');
							setTimeout(() => {window.location='reload.html';},200);" win12_title="重启"></i>
						</a>
						
					</div>
				</div>
			</div>
			<div class="pinned">
				<!-- 已固定 卡片 -->
				<div class="title">
					<p data-i18n="stmenu-pinned">已固定</p>
					<div>
						<a class="a more-btn"><span data-i18n="stmenu-allapp">所有应用</span> <i class="bi bi-chevron-right"></i></a>
						<!-- 这个好像没什么必要 -->
						<!-- 包没有必要的，为了符合宣传片而已（ from stsc -->
					</div>
				</div>
				<div class="apps">
					<a class="a sm-app enable calc" onclick="openapp('calc');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['calc','计算器'])">
						<img src="icon/calc.svg">
						<p data-i18n="calc.name">计算器</p>
					</a>
					<a class="a sm-app enable camera" onclick="apps.camera.notice();hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['camera', '相机'])">
						<img src="icon/camera.svg">
						<p data-i18n="camera.name">相机</p>
					</a>
					<a class="a sm-app enable" onclick="shownotice('feedback');">
						<img src="icon/feedback.svg">
						<p data-i18n="feedback.name">反馈中心</p>
					</a>
					<a class="a sm-app enable about" onclick="openapp('about');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['about','关于 Win12 网页版'])">
						<img src="icon/about.svg">
						<p data-i18n="about.name">关于 Win12 网页版</p>
					</a>
					<a class="a sm-app enable explorer" onclick="openapp('explorer');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['explorer','文件资源管理器'])">
						<img src="icon/explorer.svg">
						<p data-i18n="explorer.name">文件资源管理器</p>
					</a>
					<a class="a sm-app enable notepad" onclick="openapp('notepad');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['notepad','记事本'])">
						<img src="icon/notepad.svg">
						<p data-i18n="notepad.name">记事本</p>
					</a>
					<a class="a sm-app enable setting" onclick="openapp('setting');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['setting','设置'])">
						<img src="icon/setting.svg">
						<p data-i18n="setting.name">设置 </p>
					</a>
					<a class="a sm-app enable defender" onclick="openapp('defender');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['defender','Windows 安全中心'])">
						<img src="icon/defender.svg">
						<p data-i18n="defender.name">Windows 安全中心</p>
					</a>
					<a class="a sm-app enable msstore" onclick="openapp('msstore');hide_startmenu();" oncontextmenu="return showcm(event, 'smapp', ['msstore', 'Microsoft Store'])">
						<img src="icon/msstore.svg">
						<p>Microsoft Store</p>
					</a>
					<a class="a sm-app enable edge" onclick="openapp('edge');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['edge','Microsoft Edge'])">
						<img src="icon/edge.svg">
						<p>Microsoft Edge</p>
					</a>
					<a class="a sm-app enable windows12" onclick="openapp('windows12');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['windows12','Windows 12'])">
						<img src="icon/logo.svg">
						<p>Windows 12</p>
					</a>
					<a class="a sm-app enable terminal" onclick="openapp('terminal');hide_startmenu();" oncontextmenu="return showcm(event,'smapp',['terminal','终端'])">
						<img src="icon/terminal.svg">
						<p data-i18n="terminal.name">终端</p>
					</a>
				</div>
			</div>
			<div class="tuijian">
				<div class="title">
					<p data-i18n="stmenu-tj">推荐的项目</p>
					<div>
						<a class="a more-btn"><span data-i18n="more">更多</span> <i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<div class="apps">
					<a class="a tj-obj act"><img src="icon/files/ppt.png"><div><p data-i18n="stmenu-tj-f1">科学地使用瓶盖.pptx</p><p data-i18n="stmenu-tj-t1">5 分钟前</p></div></a>
					<a class="a tj-obj act"><img src="icon/files/img.png"><div><p data-i18n="stmenu-tj-f2">可口可乐瓶盖.jpg</p><p data-i18n="stmenu-tj-t2">7 分钟前</p></div></a>
					<a class="a tj-obj act"><img src="icon/files/img.png"><div><p data-i18n="stmenu-tj-f3">瓶盖构造图.jpg</p><p data-i18n="stmenu-tj-t3">16 分钟前</p></div></a>
					<a class="a tj-obj act"><img src="icon/files/word.png"><div><p data-i18n="stmenu-tj-f4">瓶盖的构造及作用.docx</p><p data-i18n="stmenu-tj-t4">24 分钟前</p></div></a>
					<a class="a tj-obj act"><img src="icon/files/excel.png"><div><p data-i18n="stmenu-tj-f5">可口可乐瓶盖厚度.xlsx</p><p data-i18n="stmenu-tj-t5">35 分钟前</p></div></a>
				</div>
			</div>
		</div>
	</div>
	<div id="search-win">
		<!-- 搜索框 -->
		<input type="text" class="input" placeholder="在这里输入你要搜索的内容" id="search-input"
			oninput="apps.search.search(this.value.length)" data-i18n-attr="placeholder" data-i18n-key="sch-ph">
		<input-before class="bi bi-search"></input-before>
		<div class="tab">
			<a class="now" data-i18n="all">全部</a><a data-i18n="sch-app">应用</a><a data-i18n="sch-doc">文档</a><a data-i18n="sch-web">网页</a>
			<a data-i18n="sch-setting">设置</a>
			<a data-i18n="sch-folder">文件夹</a><a data-i18n="sch-photo">照片</a>
		</div>
		<div class="ans">
			<div class="list">
				<list>
					<p class="text" data-i18n="sch-tj">推荐</p>
					<a onclick="openapp('setting');$('#search-btn').removeClass('show');
					$('#search-win').removeClass('show');
					setTimeout(() => {
						$('#search-win').removeClass('show-begin');
					}, 200);">
						<img src="icon/setting.svg">
						<p data-i18n="setting.name">设置</p>
					</a>
					<a onclick="openapp('about');$('#search-btn').removeClass('show');
					$('#search-win').removeClass('show');
					setTimeout(() => {
						$('#search-win').removeClass('show-begin');
					}, 200);">
						<img src="icon/about.svg">
						<p data-i18n="about.name">关于 Win12 网页版</p>
					</a>
				</list>
			</div>
			<div class="view">
				<div class="fname">
					<i class="bi bi-search"></i>
					<p class="name"></p>
					<p class="type"></p>
				</div>
				<hr>
				<div class="option">
					<list>
						<a class="a" data-i18n="sch-open">打开</a>
						<a class="a" data-i18n="sch-opfl">打开文件所在位置</a>
						<a class="a" data-i18n="sch-cpp">复制路径</a>
					</list>
				</div>
			</div>
		</div>
	</div>
	<div id="widgets">
		<div class="widgets">
			<div class="bar">
				<p class="tit" data-i18n="widget">小组件</p>
				<button onclick="shownotice('widgets')" class="a act btn btn-icon big"
					style="background: linear-gradient(100deg, var(--theme-1), var(--theme-2));color:#fff;"
					win12_title="添加"><i class="bi bi-plus-lg"></i></button>
			</div>
			<div class="content">
				<div class="template">
					<div class="calc">
						<div class="wg calc template">
							<div class="titbar">
								<a win12_title="更多" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" class="a more notip" onclick="$('.wg.calc>.titbar>.menu').toggleClass('show')"><i class="bi bi-three-dots"></i></a>
								<div class="menu">
									<a win12_title="打开应用" class="a img openapp" onclick="$('.wg.calc>.titbar>.menu').removeClass('show');hide_widgets();openapp('calc')" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)">
										<img src="icon/calc.svg">
									</a>
									<a class="a move" win12_title="移动" onmouseenter="showdescp(event)" onclick=" hidedescp(event); if (!edit_mode) { editMode(); }" onmouseleave="hidedescp(event)"><i class="bi bi-arrows-move"></i></a>
									<a class="a addtodt" win12_title="添加到桌面" onmouseenter="showdescp(event)" onclick="hidedescp(event); widgets.widgets.remove('calc'); widgets.widgets.addToDesktop('calc');" onmouseleave="hidedescp(event)"><i class="bi bi-box-arrow-in-right"></i></a>
									<a class="a delete" win12_title="删除" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="hidedescp(event); $('.wg.calc>.titbar>.menu').removeClass('show'); widgets.widgets.remove('calc');"><i class="bi bi-trash"></i></a>
								</div>
							</div>
							<div class="content" onmousedown="widgetsMove(this.parentElement, event);" ontouchstart="widgetsMove(this.parentElement, event);">
								<div class="container">
									<input id="calc-input-widgets" readonly="true" value="0" onkeydown="
									switch (event.key) {
										case '+':
											widgetCalculator.func_key(1);
											widgetCalculator.check($('*:not(.template)>*>.wg.calc>.content>.jia')[0]);
											break;
										case '-':
											widgetCalculator.func_key(2);
											widgetCalculator.check($('*:not(.template)>*>.wg.calc>.content>.jian')[0]);
											break;
										case '*':
											widgetCalculator.func_key(3);
											widgetCalculator.check($('*:not(.template)>*>.wg.calc>.content>.cheng')[0]);
											break;
										case '/':
											widgetCalculator.func_key(4);
											widgetCalculator.check($('*:not(.template)>*>.wg.calc>.content>.chu')[0]);
											break;
										case '=':
											widgetCalculator.eq();
											break;
										case 'Enter':
											widgetCalculator.eq();
											break;
										case 'Backspace':
											widgetCalculator.backspace();
											break;
										case '.':
											widgetCalculator.point();
											break;
									}
									if (!isNaN(event.key)) {
										widgetCalculator.number_key(Number(event.key));
									}
								"></input>
								</div>
								<a style="grid-area: pow;" class="b" onclick="widgetCalculator.square()">𝑥²</a>
								<a style="grid-area: sqrt;" class="b" onclick="widgetCalculator.squareRoot()">√𝑥</a>
								<a style="grid-area: c;" class="b" onclick="widgetCalculator.clear_num()">C</a>
								<a style="grid-area: jia;" class="b u jia" onclick="widgetCalculator.func_key(1); widgetCalculator.check(this);">+</a>
								<a style="grid-area: n7;" class="b" onclick="widgetCalculator.number_key(7);">7</a>
								<a style="grid-area: n8;" class="b" onclick="widgetCalculator.number_key(8);">8</a>
								<a style="grid-area: n9;" class="b" onclick="widgetCalculator.number_key(9);">9</a>
								<a style="grid-area: jian;" class="b u jian" onclick="widgetCalculator.func_key(2); widgetCalculator.check(this);">-</a>
								<a style="grid-area: n4;" class="b" onclick="widgetCalculator.number_key(4);">4</a>
								<a style="grid-area: n5;" class="b" onclick="widgetCalculator.number_key(5);">5</a>
								<a style="grid-area: n6;" class="b" onclick="widgetCalculator.number_key(6);">6</a>
								<a style="grid-area: cheng;" class="b u hceng" onclick="widgetCalculator.func_key(3); widgetCalculator.check(this);">×</a>
								<a style="grid-area: n1;" class="b" onclick="widgetCalculator.number_key(1);">1</a>
								<a style="grid-area: n2;" class="b" onclick="widgetCalculator.number_key(2);">2</a>
								<a style="grid-area: n3;" class="b" onclick="widgetCalculator.number_key(3);">3</a>
								<a style="grid-area: chu;" class="b u chu" onclick="widgetCalculator.func_key(4); widgetCalculator.check(this);">÷</a>
								<a style="grid-area: dot;" class="b" onclick="widgetCalculator.point()">.</a>
								<a style="grid-area: n0;" class="b" onclick="widgetCalculator.number_key(0);">0</a>
								<a style="grid-area: back;" class="b" onclick="widgetCalculator.backspace()"><i class="bi bi-backspace"></i></a>
								<a style="grid-area: ans;" class="b ans u" onclick="if(!widgetCalculator.eq()){widgetCalculator.clear_num(); shownotice('ZeroDivision')}">=</a>
							</div>
						</div>
					</div>
					<div class="weather">
						<div class="wg weather template">
							<div class="titbar">
								<a win12_title="更多" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" class="a more notip" onclick="$('.wg.weather>.titbar>.menu').toggleClass('show')"><i class="bi bi-three-dots"></i></a>
								<div class="menu">
									<a class="a move" win12_title="移动" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="hidedescp(event); if (!edit_mode) { editMode() }; "><i class="bi bi-arrows-move"></i></a>
									<a class="a addtotb" win12_title="添加到任务栏" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="hidedescp(event); widgets.widgets.remove('weather'); widgets.widgets.addToToolbar('weather'); "><i class="bi bi-menu-app"></i></a>
									<a class="a addtodt" win12_title="添加到桌面" onmouseenter="showdescp(event)" onclick="hidedescp(event); widgets.widgets.remove('weather'); widgets.widgets.addToDesktop('weather'); " onmouseleave="hidedescp(event)"><i class="bi bi-box-arrow-in-right"></i></a>
									<a class="a delete" win12_title="删除" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="hidedescp(event); $('.wg.weather>.titbar>.menu').removeClass('show'); widgets.widgets.remove('weather'); "><i class="bi bi-trash"></i></a>
								</div>
							</div>
							<div class="content" onmousedown="widgetsMove(this.parentElement, event);" ontouchstart="widgetsMove(this.parentElement, event);">
								<img class="img" />
								<div class="text">
									<p class="temperature">--℃</p>
									<p class="detail">-- 体感温度 --℃</p>
								</div>
							</div>
						</div>
					</div>
					<div class="monitor">
						<div class="wg monitor template">
							<div class="titbar">
								<a win12_title="更多" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" class="a more notip" onclick="$('.wg.monitor>.titbar>.menu').toggleClass('show')"><i class="bi bi-three-dots"></i></a>
								<div class="menu">
									<a win12_title="打开应用" class="a img openapp" onclick="$('.wg.calc>.titbar>.menu').removeClass('show');hide_widgets();openapp('taskmgr')" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)">
										<img src="icon/taskmgr.png">
									</a>
									<a class="a chose" win12_title="切换监视器类型" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="showdescp(event); shownotice('widgets.monitor'); "><i class="bi bi-arrow-left-right"></i></a>
									<a class="a move" win12_title="移动" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="showdescp(event); if (!edit_mode) { editMode(); } "><i class="bi bi-arrows-move"></i></a>
									<a class="a addtotb" win12_title="添加到任务栏" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="showdescp(event); widgets.widgets.remove('monitor'); widgets.widgets.addToToolbar('monitor'); "><i class="bi bi-menu-app"></i></a>
									<a class="a addtodt" win12_title="添加到桌面" onmouseenter="showdescp(event)" onclick="showdescp(event); widgets.widgets.remove('monitor'); widgets.widgets.addToDesktop('monitor'); " onmouseleave="hidedescp(event)"><i class="bi bi-box-arrow-in-right"></i></a>
									<a class="a delete" win12_title="删除" onmouseenter="showdescp(event)" onmouseleave="hidedescp(event)" onclick="showdescp(event); $('.wg.monitor>.titbar>.menu').removeClass('show'); widgets.widgets.remove('monitor'); "><i class="bi bi-trash"></i></a>
								</div>
							</div>
							<div class="content" onmousedown="widgetsMove(this.parentElement, event);" ontouchstart="widgetsMove(this.parentElement, event);">
								<div class="container">
									<svg width="60px" height="60px">
										<circle cx="30px" cy="30px" r="26px"></circle>
										<circle cx="30px" cy="-30px" r="26px"></circle>
									</svg>
									<div class="text">
										<div class="value">50%</div>
									</div>
								</div>
								<div class="text">
									<p class="type">内存</p>
									<p class="value">50%</p>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="grid">

				</div>
			</div>
		</div>
		<span class="hr"></span>
		<div class="news">
			<div class="bar">
				<p class="tit" data-i18n="widget-news">新闻</p>
				<div>
					<span style="color: #7f7f7f;margin-right: 5px;margin-top: 2px;" data-i18n="widget-news-res">我们不对新闻内容负责</span>
					<button onclick="shownotice('widgets.news.source')" class="a act btn btn-icon big"
						style="background: linear-gradient(100deg, var(--theme-1), var(--theme-2));color:#fff;"
						win12_title="切换新闻源"><i class="bi bi-arrow-repeat"></i></button>
				</div>
			</div>
			<div class="full-tip">
				<loading>
					<svg width="30px" height="30px" viewBox="0 0 16 16">
						<circle cx="8px" cy="8px" r="7px" style="stroke:#7f7f7f50;fill:none;stroke-width:3px;"></circle>
						<circle cx="8px" cy="8px" r="7px" style="stroke:#2983cc;stroke-width:3px;"></circle>
					</svg>
				</loading>
				<p class="tit"></p>
				<p class="desc"></p>
				<p class="info"></p>
			</div>
			<div class="content">
				<div class="card top-news"></div>
				<div class="news-all"></div>
			</div>
		</div>
	</div>
	<div id="copilot">
		<div class="titbar">
			<p class="text">Windows 12 Copilot</p>
			<div class="alr">
				<span class="btn about" onclick="shownotice('about-copilot')">
					<i class="bi bi-info-circle"></i>
				</span>
				<span class="btn hide" onclick="$('#copilot').removeClass('show')">
					<i class="bi bi-chevron-bar-right"></i>
				</span>
			</div>
		</div>
		<p style="margin-top: 5px;padding: 8px 15px;background-image: radial-gradient(circle at 20px 5px,rgb(189, 104, 232) 10px,rgb(42, 72, 156) 70%);color: #fff;">
			AI Copilot 震撼回归，无地域限制，遥遥领先于微软</p>
		<div class="chat">
			<a class="a btn button" onclick="copilot.init()" style="display: inline-block;margin-top: 10px;" data-i18n="copilot.start">开始对话</a>
			<p style="margin-top: 10px;" data-i18n="copilot.p">使用前请先点击右上 "<i class="bi bi-info-circle"></i>" 查看使用注意事项</p>
		</div>
		<div class="inputbox disable">
			<input type="text" class="input" onkeydown="if(event.keyCode==13){copilot.send($(this).val());$(this).val('');this.blur()}">
			<a class="a send btn" onclick="copilot.send($('#copilot>.inputbox>.input').val());$('#copilot>.inputbox>.input').val('');" data-i18n="copilot.send">发送</a>
		</div>
	</div>
	<div id="control">
		<div class="cont">
			<div class="top">
				<div class="btn1">
					<div class="icon active" onclick="controlStatus.call(this, 'wifi')">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit">WLAN</div>
				</div>
				<div class="btn2">
					<div class="icon active" onclick="controlStatus.call(this)">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit" data-i18n="ctrl-blue">蓝牙</div>
				</div>
				<div class="btn3">
					<div class="icon" onclick="controlStatus.call(this, 'fly')">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit" data-i18n="ctrl-air">飞行模式</div>
				</div>
				<div class="btn4">
					<div class="icon" onclick="controlStatus.call(this, 'dark')">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit" data-i18n="ctrl-eye">护眼模式</div>
				</div>
				<div class="btn5">
					<div class="icon" onclick="controlStatus.call(this)">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit" data-i18n="ctrl-hotspot">移动热点</div>
				</div>
				<div class="btn6">
					<div class="icon" onclick="controlStatus.call(this)">
						<icon class="s-icon"></icon>
					</div>
					<div class="tit" data-i18n="ctrl-acc">辅助功能</div>
				</div>
			</div>
			<div class="bottom">
				<div class="brightness">
					<div class="icon">
						<icon class="s-icon"></icon>
					</div>
					<div class="range-container" onmousedown="dragBrightness(event)" ontouchstart="dragBrightness(event)">
						<div class="after"></div>
						<div class="slider-btn"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="datebox">
		<div class="tit">
			<p class="time">12:29:19</p>
			<p class="date">星期一, 2022年09月22日</p>
			<hr>
		</div>
		<div class="cont">
			<div class="head" data-i18n="data-week">
				<p>一</p><p>二</p><p>三</p><p>四</p><p>五</p><p>六</p><p>日</p>
			</div>
			<div class="body">
			</div>
		</div>
	</div>
	<div id="dock-box">
		<!-- Dock(任务)栏 -->
		<div class="dock dock-start">
			<a class="dock-btn" id="start-btn" win12_title="开始 (Ctrl+Win)" onclick="openDockWidget('start-menu')" oncontextmenu="return showcm(event,'winx',null)">
				<!-- 开始按钮 -->
				<svg class="menu" viewBox="0,0,440,439" xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden">
					<defs>
						<clipPath id="clip-start1">
							<rect x="134" y="59" width="440" height="439" />
						</clipPath>
					</defs>
					<g clip-path="url(#clip-start1)" transform="translate(-134 -59)">
						<path
							d="M233.479 67.5001 475.521 67.5001C525.767 67.5001 566.5 108.233 566.5 158.479L566.5 490.5 142.5 490.5 142.5 158.479C142.5 108.233 183.233 67.5001 233.479 67.5001Z"
							stroke="#7F7F7F" stroke-width="14.6667" stroke-linecap="round" stroke-miterlimit="8"
							fill="#A6A6A6" fill-rule="evenodd" style="fill: var(--bg70);" />
						<path d="M208.5 132.5 334.716 132.5" stroke="#888" stroke-width="14.6667" stroke-linecap="round"
							stroke-miterlimit="8" fill="none" fill-rule="evenodd" />
						<path d="M208.5 175.5 386.34 175.5" stroke="#888" stroke-width="14.6667" stroke-linecap="round"
							stroke-miterlimit="8" fill="none" fill-rule="evenodd" />
						<path d="M208.5 221.5 271.608 221.5" stroke="#888" stroke-width="14.6667" stroke-linecap="round"
							stroke-miterlimit="8" fill="none" fill-rule="evenodd" />
					</g>
				</svg>
				<img src="icon/logo.svg" class="ico">
			</a>
			<a class="dock-btn" id="search-btn" win12_title="搜索" onclick="openDockWidget('search-win')">
				<!--搜索按钮-->
				<svg class="out" width="26" height="26" viewBox="0,0,228,229" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="clip-search-out-0"><rect x="548" y="268" width="228" height="229"/></clipPath><linearGradient x1="738.799" y1="300.076" x2="608.064" y2="486.786" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-search-out-1"><stop offset="0" stop-color="#A6A6A6"/><stop offset="0.18" stop-color="#A6A6A6"/><stop offset="0.91" stop-color="#595959"/><stop offset="1" stop-color="#595959"/></linearGradient></defs><g clip-path="url(#clip-search-out-0)" transform="translate(-548 -268)"><path d="M640 268.696C690.41 268.696 731.276 309.574 731.276 360 731.276 378.91 725.529 396.477 715.687 411.049L713.816 413.318 771.4 470.903C777.068 476.571 777.068 485.761 771.4 491.429 765.732 497.097 756.542 497.097 750.874 491.429L693.292 433.847 691.033 435.711C676.465 445.556 658.904 451.305 640 451.304 589.59 451.305 548.724 410.426 548.724 360 548.724 309.574 589.59 268.696 640 268.696Z" fill="url(#fill-search-out-1)" fill-rule="evenodd"/></g></svg>
				<svg class="in" width="20" height="20" viewBox="0,0,134,134" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="clip-search-in-0"><rect x="573" y="293" width="134" height="134"/></clipPath><linearGradient x1="611.068" y1="280.509" x2="668.932" y2="439.491" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-search-in-1"><stop offset="0" stop-color="#82CDF6"/><stop offset="0.18" stop-color="#82CDF6"/><stop offset="0.91" stop-color="#4297D6"/><stop offset="1" stop-color="#4297D6"/></linearGradient><image width="132" height="132" xlink:href="data:image/png;base64,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" preserveAspectRatio="none" id="img2"></image><clipPath id="clip-search-in-3"><rect x="574" y="294" width="132" height="132"/></clipPath></defs><g clip-path="url(#clip-search-in-0)" transform="translate(-573 -293)"><path d="M574 360C574 323.549 603.549 294 640 294 676.451 294 706 323.549 706 360 706 396.451 676.451 426 640 426 603.549 426 574 396.451 574 360Z" fill="url(#fill-search-in-1)" fill-rule="evenodd"/><g clip-path="url(#clip-search-in-3)"><use width="100%" height="100%" xlink:href="#img2" transform="translate(574 294)"></use></g></g></svg>
			</a>
			<a class="dock-btn" id="widgets-btn" win12_title="小组件" onclick="news.setup(); openDockWidget('widgets')">
				<!--小组件按钮-->
				<svg viewBox="0,0,260,260" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden" class="wid1"><defs><clipPath id="clip-wid1-0"><rect x="510" y="230" width="260" height="260"/></clipPath><linearGradient x1="551.891" y1="207.391" x2="728.109" y2="512.609" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-wid1-1"><stop offset="0" stop-color="#7AB1FA"/><stop offset="0.18" stop-color="#7AB1FA"/><stop offset="0.91" stop-color="#4C6EDC"/><stop offset="1" stop-color="#4C6EDC"/></linearGradient></defs><g clip-path="url(#clip-wid1-0)" transform="translate(-510 -230)"><path d="M511 274.001C511 250.252 530.252 231 554.001 231L725.999 231C749.748 231 769 250.252 769 274.001L769 445.999C769 469.748 749.748 489 725.999 489L554.001 489C530.252 489 511 469.748 511 445.999Z" fill="url(#fill-wid1-1)" fill-rule="evenodd"/></g></svg>
				<svg viewBox="0,0,175,206" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden" class="wid2"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.81042 5.77778"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><clipPath id="clip-wid2-4"><rect x="615" y="214" width="175" height="206"/></clipPath><clipPath id="clip-wid2-5"><rect x="-6.53674" y="-6.5" width="101.068" height="115"/></clipPath><clipPath id="clip-wid2-6"><rect x="0" y="0" width="89" height="104"/></clipPath><linearGradient x1="644.325" y1="249.245" x2="754.675" y2="380.755" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-wid2-7"><stop offset="0" stop-color="#775CFE"/><stop offset="0.43" stop-color="#775CFE"/><stop offset="0.83" stop-color="#2473DC"/><stop offset="1" stop-color="#2473DC"/></linearGradient><clipPath id="clip-wid2-8"><rect x="-5" y="-5" width="67" height="42"/></clipPath><clipPath id="clip-wid2-9"><rect x="0" y="0" width="57" height="35"/></clipPath><clipPath id="clip-wid2-10"><rect x="-5" y="-5" width="103" height="42"/></clipPath><clipPath id="clip-wid2-11"><rect x="0" y="0" width="93" height="35"/></clipPath><clipPath id="clip-wid2-12"><rect x="-5" y="-5" width="80" height="42"/></clipPath><clipPath id="clip-wid2-13"><rect x="0" y="0" width="72" height="35"/></clipPath></defs><g clip-path="url(#clip-wid2-4)" transform="translate(-615 -214)"><g clip-path="url(#clip-wid2-5)" filter="url(#fx0)" transform="matrix(1.98876 0 0 2 614 213)"><g clip-path="url(#clip-wid2-6)"><path d="M18.2408 33.043C18.2408 24.8162 24.9475 18.1472 33.2208 18.1472L56.0574 18.1472C64.3306 18.1472 71.0374 24.8162 71.0374 33.043L71.0374 71.2513C71.0374 79.4781 64.3306 86.1472 56.0574 86.1472L33.2208 86.1472C24.9475 86.1472 18.2408 79.4781 18.2408 71.2513Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M647 276.792C647 260.338 660.338 247 676.792 247L722.208 247C738.662 247 752 260.338 752 276.792L752 353.208C752 369.662 738.662 383 722.208 383L676.792 383C660.338 383 647 369.662 647 353.208Z" fill="url(#fill-wid2-7)" fill-rule="evenodd"/><g clip-path="url(#clip-wid2-8)" filter="url(#fx1)" transform="translate(649 260)"><g clip-path="url(#clip-wid2-9)"><path d="M17.3857 17.3857 39.7776 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M664.5 275.5 686.892 275.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><g clip-path="url(#clip-wid2-10)" filter="url(#fx2)" transform="translate(649 282)"><g clip-path="url(#clip-wid2-11)"><path d="M17.3857 17.3857 75.9099 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M664.5 297.5 723.024 297.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><g clip-path="url(#clip-wid2-12)" filter="url(#fx3)" transform="translate(649 305)"><g clip-path="url(#clip-wid2-13)"><path d="M17.3857 17.3857 54.5358 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M664.5 320.5 701.65 320.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></svg>
				<svg viewBox="0,0,174,298" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden" class="wid3"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.81079 5.7971"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><clipPath id="clip-wid3-4"><rect x="498" y="214" width="174" height="298"/></clipPath><clipPath id="clip-wid3-5"><rect x="-6.53714" y="-6.52174" width="98.0572" height="162.04"/></clipPath><clipPath id="clip-wid3-6"><rect x="0" y="0" width="88" height="150"/></clipPath><linearGradient x1="668.609" y1="288.246" x2="496.391" y2="432.754" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-wid3-7"><stop offset="0" stop-color="#C45DD5"/><stop offset="0.18" stop-color="#C45DD5"/><stop offset="0.69" stop-color="#A266E4"/><stop offset="1" stop-color="#A266E4"/></linearGradient><clipPath id="clip-wid3-8"><rect x="-5" y="-5" width="86" height="42"/></clipPath><clipPath id="clip-wid3-9"><rect x="0" y="0" width="77" height="35"/></clipPath><clipPath id="clip-wid3-10"><rect x="-5" y="-5" width="67" height="42"/></clipPath><clipPath id="clip-wid3-11"><rect x="0" y="0" width="58" height="35"/></clipPath><clipPath id="clip-wid3-12"><rect x="-5" y="-5" width="92" height="42"/></clipPath><clipPath id="clip-wid3-13"><rect x="0" y="0" width="85" height="35"/></clipPath></defs><g clip-path="url(#clip-wid3-4)" transform="translate(-498 -214)"><g clip-path="url(#clip-wid3-5)" filter="url(#fx0)" transform="matrix(1.98864 0 0 1.99333 498 214)"><g clip-path="url(#clip-wid3-6)" transform="translate(2.84217e-14 -1.42109e-14)"><path d="M18.0166 36.3921C18.0166 26.2201 26.2821 17.9741 36.4781 17.9741L51.3493 17.9741C61.5454 17.9741 69.8109 26.2201 69.8109 36.3921L69.8109 113.436C69.8109 123.608 61.5454 131.854 51.3493 131.854L36.4781 131.854C26.2821 131.854 18.0166 123.608 18.0166 113.436Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M531 283.713C531 263.437 547.437 247 567.713 247L597.287 247C617.563 247 634 263.437 634 283.713L634 437.287C634 457.563 617.563 474 597.287 474L567.713 474C547.437 474 531 457.563 531 437.287Z" fill="url(#fill-wid3-7)" fill-rule="evenodd"/><g clip-path="url(#clip-wid3-8)" filter="url(#fx1)" transform="translate(534 259)"><g clip-path="url(#clip-wid3-9)"><path d="M17.3857 17.3857 59.5683 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M549.5 274.5 591.683 274.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><g clip-path="url(#clip-wid3-10)" filter="url(#fx2)" transform="translate(534 281)"><g clip-path="url(#clip-wid3-11)"><path d="M17.3857 17.3857 40.3525 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M549.5 296.5 572.467 296.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/><g clip-path="url(#clip-wid3-12)" filter="url(#fx3)" transform="translate(534 302)"><g clip-path="url(#clip-wid3-13)"><path d="M17.3857 17.3857 67.3293 17.3858" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M549.5 317.5 599.444 317.5" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></svg>
				<svg viewBox="0,0,141,113" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden" class="wid4"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="5.77778 5.77778"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0 0"/><feFuncG type="discrete" tableValues="0 0"/><feFuncB type="discrete" tableValues="0 0"/><feFuncA type="linear" slope="0.4" intercept="0"/></feComponentTransfer><feGaussianBlur stdDeviation="4 4"/></filter><clipPath id="clip-wid4-2"><rect x="629" y="380" width="141" height="113"/></clipPath><clipPath id="clip-wid4-3"><rect x="-6" y="-3" width="154" height="121"/></clipPath><clipPath id="clip-wid4-4"><rect x="0" y="0" width="143" height="115"/></clipPath><linearGradient x1="670.387" y1="386.075" x2="728.613" y2="486.925" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="fill-wid4-5"><stop offset="0" stop-color="#D854CB"/><stop offset="0.13" stop-color="#D854CB"/><stop offset="1" stop-color="#A266E4"/></linearGradient><clipPath id="clip-wid4-6"><rect x="-5" y="-5" width="105" height="69"/></clipPath><clipPath id="clip-wid4-7"><rect x="0" y="0" width="98" height="63"/></clipPath></defs><g clip-path="url(#clip-wid4-2)" transform="translate(-629 -380)"><g clip-path="url(#clip-wid4-3)" filter="url(#fx0)" transform="translate(628 379)"><g clip-path="url(#clip-wid4-4)"><path d="M18.97 46.5177C18.97 31.4582 31.1781 19.25 46.2377 19.25L96.7623 19.25C111.822 19.25 124.03 31.4582 124.03 46.5177L124.03 68.4823C124.03 83.5418 111.822 95.75 96.7623 95.75L46.2377 95.75C31.1781 95.75 18.97 83.5418 18.97 68.4823Z" fill="#FF0000" fill-rule="evenodd"/></g></g><path d="M648 425.733C648 410.969 659.969 399 674.733 399L724.267 399C739.031 399 751 410.969 751 425.733L751 447.267C751 462.031 739.031 474 724.267 474L674.733 474C659.969 474 648 462.031 648 447.267Z" fill="url(#fill-wid4-5)" fill-rule="evenodd"/><g clip-path="url(#clip-wid4-6)" filter="url(#fx1)" transform="translate(653 407)"><g clip-path="url(#clip-wid4-7)"><path d="M17.3857 45.3857C24.6516 31.6825 31.9177 17.9794 39.5617 17.3963 47.2057 16.8131 56.4457 40.5542 63.2497 41.887 70.0537 43.2198 79.3357 31.3285 80.3857 25.3933" stroke="#FFFFFF" stroke-width="9" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></g><path d="M668.5 450.5C675.766 436.797 683.032 423.094 690.676 422.511 698.32 421.928 707.56 445.669 714.364 447.001 721.168 448.334 730.45 436.443 731.5 430.508" stroke="#FFFFFF" stroke-width="8.66667" stroke-linecap="round" stroke-miterlimit="8" fill="none" fill-rule="evenodd"/></g></svg>
			</a>
			<a class="dock-btn" onclick="$('#copilot').toggleClass('show');$(this).toggleClass('show')" id="copilot-btn">
				<!--copilot按钮-->
			<img src="icon/copilot.svg"></a>
		</div>
		<div class="dock" id="taskbar" style="display: none;" count="0">
		</div>
		<div id="toolbar">

		</div>
		<div class="a dock theme" win12_title="切换主题" onclick="toggletheme()">
			<!--切换主题-->
			<svg viewBox="0,0,307,307" class="light" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="theme-light-clip0"><rect x="79" y="77" width="307" height="307" /></clipPath><linearGradient x1="128.499" y1="49.9991" x2="337.501" y2="412.001" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="theme-light-fill1"><stop offset="0" stop-color="#FFC567" /><stop offset="0.18" stop-color="#FFC567" /><stop offset="0.8" stop-color="#EE54F2" /></linearGradient></defs><g clip-path="url(#theme-light-clip0)" transform="translate(-79 -77)"><path d="M80 231C80 146.5 148.5 78 233 78 317.5 78 386 146.5 386 231 386 315.5 317.5 384 233 384 148.5 384 80 315.5 80 231Z" fill="url(#theme-light-fill1)" fill-rule="evenodd" /></g></svg>
			<svg viewBox="0,0,275,294" class="dark" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="theme-dark-clip0"><rect x="525" y="230" width="275" height="294" /></clipPath><linearGradient x1="599.492" y1="203.887" x2="725.508" y2="550.113" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="theme-dark-fill1"><stop offset="0" stop-color="#2474B6" /><stop offset="0.42" stop-color="#2474B6" /><stop offset="0.98" stop-color="#FFD966" /><stop offset="1" stop-color="#FFD966" /></linearGradient></defs><g clip-path="url(#theme-dark-clip0)" transform="translate(-525 -230)"><path d="M635.002 236.062C638.664 240.491 639.245 246.694 636.469 251.721 625.869 271.094 620.334 292.809 620.372 314.869 620.372 388.255 680.469 447.679 754.537 447.679 764.199 447.679 773.604 446.675 782.643 444.759 788.307 443.538 794.156 445.809 797.493 450.526 801.036 455.459 800.803 462.148 796.925 466.824 767.803 502.434 724.103 523.073 677.976 523 593.458 523 525 455.217 525 371.701 525 308.846 563.757 254.933 618.942 232.083 624.588 229.708 631.132 231.329 635.002 236.062Z" fill="url(#theme-dark-fill1)" fill-rule="evenodd" /></g></svg>
		</div>
		<div class="a dock control" onclick="openDockWidget('control')">
			<!--控制-->
			<svg viewBox="1 7 29 15" width="28px" height="14px" version="1.1" id="svg4">
				<path
					d="M 4 7 C 2.3550302 7 1 8.3550302 1 10 L 1 19 C 1 20.64497 2.3550302 22 4 22 L 24 22 C 25.64497 22 27 20.64497 27 19 L 27 10 C 27 8.3550302 25.64497 7 24 7 L 4 7 z M 4 9 L 24 9 C 24.56503 9 25 9.4349698 25 10 L 25 19 C 25 19.56503 24.56503 20 24 20 L 4 20 C 3.4349698 20 3 19.56503 3 19 L 3 10 C 3 9.4349698 3.4349698 9 4 9 z M 5 11 L 5 18 L 23 18 L 23 11 L 5 11 z M 28 12 L 28 17 L 29 17 C 29.552 17 30 16.552 30 16 L 30 13 C 30 12.448 29.552 12 29 12 L 28 12 z"
					id="path2" />
			</svg>
			<icon class="s-icon"></icon>
			<icon class="s-icon"></icon>
		</div>
		<div class="a dock date" onclick="openDockWidget('datebox')">
			<!--日期-->
			<p class="time">12:34:56</p>
			<p class="date">2022/10/01</p>
			<i class="bi bi-chevron-down"></i>
		</div>
	</div>
	<div id="desktop">
		<h1>请使用相对正常的浏览器访问。(出现错误：无法处理桌面图标)</h1>
		<h1>請使用相對正常的瀏覽器訪問。</h1>
		<h1>Please use a common browser to access.</h1>
		<span class="choose">
		</span>
		<p style="background-color: rgba(11,45,14,0);z-index:1;position: absolute;top:0px;left:0px;height:100%;width:100%" oncontextmenu="return showcm(event,'desktop');"></p>
	</div>
	<!-- 右键菜单 -->
	<div id="cm" tabindex="-1">
		<input class="foc">
		<list class="new">
		</list>
	</div>
	<!-- 下拉菜单 -->
	<div id="dp">
		<list>
		</list>
	</div>
	<!-- 悬停提示 -->
	<div id="descp">

	</div>

	<div id="desktop-widgets">
		<div class="widgets-move"></div>
	</div>
	<div id="desktop-editbar-container">
		<div id="desktop-editbar">
			<div onclick="shownotice('widgets.desktop');"><i class="bi bi-plus"></i> <span data-i18n="editmd-add">添加小组件</span></div>
			<div onclick="openapp('setting');$('#win-setting > div.menu > list > a.enable.appearance')[0].click();"><i class="bi bi-brush"></i> <span data-i18n="psnl">个性化</span></div>
			<div onclick="editMode();" win12_title="退出编辑模式"><i class="bi bi-x-lg"></i></div>
		</div>
	</div>
	<p id="translater" style="display: none;"></p>
	<!-- 提示窗 -->
	<div id="notice-back">
		<div id="notice">
			<div class="cnt">
				<p class="tit">快跑！</p>
				<p>你的电脑即将爆炸</p>
			</div>
			<div class="btns">
				<a class="a btn main" onclick="closenotice()">确定</a>
			</div>
		</div>
	</div>
	<div class="window defender" data-min-width="800" style="width: max(800px, 70%)">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/defender.svg" class="icon">
			<p data-i18n="defender.name">Windows 安全中心</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('defender')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('defender')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('defender')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/defender.svg" class="icon">
		</div>
		<div class="content" id="win-defender">
			<div class="app-left">
				<button class="close-menu">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
						stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
						class="feather feather-x">
						<line x1="18" y1="6" x2="6" y2="18" />
						<line x1="6" y1="6" x2="18" y2="18" />
					</svg>
				</button>
				<ul class="nav-list">
					<li class="nav-list-item active">
						<a class="nav-list-link">
							<icon></icon>
							<span data-i18n="home">主页</span>
						</a>
					</li>
					<li class="nav-list-item">
						<a class="nav-list-link">
							<icon></icon>
							<span data-i18n="defender.virus">病毒威胁与防护</span>
						</a>
					</li>
					<li class="nav-list-item">
						<a class="nav-list-link">
							<icon></icon>
							<span data-i18n="defender.account">账户防护</span>
						</a>
					</li>
					<li class="nav-list-item">
						<a class="nav-list-link">
							<icon></icon>
							<span data-i18n="defender.firewall">防火墙和网络防护</span>
						</a>
					</li>
					<li class="nav-list-item">
						<a class="nav-list-link">
							<icon></icon>
							<span data-i18n="defender.device">设备安全性</span>
						</a>
					</li>
				</ul>
			</div>
			<div class="app-main">
				<div class="main-header-line">
					<h1 data-i18n="defender.glance">安全性概览</h1>
					<div class="action-buttons">
						<button class="open-right-area">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
								fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
								stroke-linejoin="round" class="feather feather-activity">
								<polyline points="22 12 18 12 15 21 9 3 6 12 2 12" />
							</svg>
						</button>
						<button class="menu-button">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
								fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
								stroke-linejoin="round" class="feather feather-menu">
								<line x1="3" y1="12" x2="21" y2="12" />
								<line x1="3" y1="6" x2="21" y2="6" />
								<line x1="3" y1="18" x2="21" y2="18" />
							</svg>
						</button>
					</div>
				</div>
				<div class="chart-row three">
					<div class="chart-container-wrapper">
						<div class="chart-container">
							<div class="chart-info-wrapper">
								<h2 data-i18n="defender.network">网络流量使用</h2>
								<span>20.5 M</span>
							</div>
							<div class="chart-svg">
								<svg viewBox="0 0 36 36" class="circular-chart pink">
									<path class="circle-bg" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<path class="circle" stroke-dasharray="30, 100" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<text x="18" y="20.35" class="percentage">30%</text>
								</svg>
							</div>
						</div>
					</div>
					<div class="chart-container-wrapper">
						<div class="chart-container">
							<div class="chart-info-wrapper">
								<h2 data-i18n="defender.speed">网络速度</h2>
								<span>99 M/S</span>
							</div>
							<div class="chart-svg">
								<svg viewBox="0 0 36 36" class="circular-chart blue">
									<path class="circle-bg" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<path class="circle" stroke-dasharray="60, 100" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<text x="18" y="20.35" class="percentage">99M/S</text>
								</svg>
							</div>
						</div>
					</div>
					<div class="chart-container-wrapper">
						<div class="chart-container">
							<div class="chart-info-wrapper">
								<h2 data-i18n="defender.clearv">入侵病毒清除</h2>
								<h3 style="color: white;" data-i18n="defender.allvc">所有病毒均被清除</h3>
							</div>
							<div class="chart-svg">
								<svg viewBox="0 0 36 36" class="circular-chart orange">
									<path class="circle-bg" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<path class="circle" stroke-dasharray="100, 100" d="M18 2.0845
							a 15.9155 15.9155 0 0 1 0 31.831
							a 15.9155 15.9155 0 0 1 0 -31.831"></path>
									<text x="18" y="20.35" class="percentage">100%</text>
								</svg>
							</div>
						</div>
					</div>
				</div>
				<div class="chart-row two">
					<div class="chart-container-wrapper big">
						<div class="chart-container">
							<div class="chart-container-header">
								<h2 data-i18n="defender.freq">电脑受病毒攻击频率</h2>
								<span>过去 30 天</span>
							</div>
							<div class="line-chart">
								<canvas id="chart"></canvas>
							</div>
							<div class="chart-data-details">
								<div class="chart-details-header"></div>
							</div>
						</div>
					</div>
					<div class="chart-container-wrapper small">
						<div class="chart-container">
							<div class="chart-container-header">
								<h2 data-i18n="defender.netreq">网络通信比例</h2>
								<span href="#" data-i18n="defender.netreq-thismonth">这个月</span>
							</div>
							<div class="acquisitions-bar">
								<span class="bar-progress rejected" style="width:1%;"></span>
								<span class="bar-progress shortlisted" style="width:5%;"></span>
								<span class="bar-progress on-hold" style="width:19%;"></span>
								<span class="bar-progress applications" style="width:80%;"></span>
							</div>
							<div class="progress-bar-info">
								<span class="progress-color applications"></span>
								<span class="progress-type">请求成功</span>
								<span class="progress-amount">80%</span>
							</div>
							<div class="progress-bar-info">
								<span class="progress-color shortlisted"></span>
								<span class="progress-type">请求失败</span>
								<span class="progress-amount">5%</span>
							</div>
							<div class="progress-bar-info">
								<span class="progress-color on-hold"></span>
								<span class="progress-type">请求发出后无响应</span>
								<span class="progress-amount">19%</span>
							</div>
							<div class="progress-bar-info">
								<span class="progress-color rejected"></span>
								<span class="progress-type">含有病毒</span>
								<span class="progress-amount">1%</span>
							</div>
						</div>
						<div class="chart-container applicants">
							<div class="chart-container-header">
								<h2 data-i18n="defender.recreq">请求记录</h2>
								<span>今天 仅展示最近5次请求</span>
							</div>
							<div class="applicant-line">
								<div class="applicant-info">
									<span>Https-Get</span>
									<p>Get url : <strong>tjy-gitnub.github......</strong></p>
								</div>
							</div>
							<div class="applicant-line">
								<div class="applicant-info">
									<span>Https-Post</span>
									<p>Post url :<strong>api.github.com/r......</strong></p>
								</div>
							</div>
							<div class="applicant-line">
								<div class="applicant-info">
									<span>Https-Get</span>
									<p>Get url :<strong>cdn.jsdelivr.net/p......</strong></p>
								</div>
							</div>
							<div class="applicant-line">
								<div class="applicant-info">
									<span>Https-Get</span>
									<p>Get url :<strong>win12server.freeh......</strong></p>
								</div>
							</div>
							<div class="applicant-line">
								<div class="applicant-info">
									<span>Https-Get</span>
									<p>Get url :<strong>github.com/tjy-g......</strong></p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="window-fill"></div>

	<!-- Microsoft Store @User782Tec (2023/10/3) -->
	<div class="window msstore">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/msstore.svg" class="icon">
			<p>Microsoft Store</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('msstore');"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('msstore')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('msstore')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback" data-delay="3500">
			<img src="icon/msstore.svg" class="icon">
            <loading style="position: static; ">
                <svg width="40px" height="40px" viewBox="0 0 16 16">
                    <!-- <circle cx="8px" cy="8px" r="7px" style="stroke:#7f7f7f50;fill:none;stroke-width:3px;"></circle> -->
                    <circle cx="8px" cy="8px" r="7px" style="stroke:#2983cc;stroke-width:3px;"></circle>
                </svg>
            </loading>
		</div>

		<div class="content" id="win-msstore">
			<div class="menu">
				<list class="focs">
					<a class="home check" onclick="apps.msstore.page('home');"><icon></icon><span class="t2" data-i18n="home">首页</span></a>
					<a class="apps" onclick="apps.msstore.page('apps');"><icon></icon><span class="t2" data-i18n="msstore.apps">应用</span></a>
					<a class="game" onclick="apps.msstore.page('game');"><icon></icon><span class="t2" data-i18n="msstore.games">游戏</span></a>
					<a class="down" onclick="apps.msstore.page('down');"><icon></icon><span class="t2" data-i18n="msstore.download">下载</span></a>
					<span class="focs"></span>
				</list>
			</div>
			<div class="page">
				<div class="cnt home show">
					<div class="container">
						<div class="cards"></div>
						<div class="apps">
							<div class="tit" data-i18n="msstore.topfree">热门免费应用 ></div>
							<div class="app-cards">
								<div class="card1">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name">Visual Studio Code</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.devtool">开发</div>
											</div>
										</div>
									</div>
								</div>
								<div class="card2">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name" data-i18n="msstore.genimp">原神</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.game">游戏</div>
											</div>
										</div>
									</div>
								</div>
								<div class="card3">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name" data-i18n="msstore.mc">我的世界</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.game">游戏</div>
											</div>
										</div>
									</div>
								</div>
								<div class="card4">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name">Krita</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.design">设计</div>
											</div>
										</div>
									</div>
								</div>
								<div class="card5">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name">VirtualBox</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.tool">工具</div>
											</div>
										</div>
									</div>
								</div>
								<div class="card6">
									<div class="left"></div>
									<div class="right">
										<div class="tit">
											<div class="up">
												<div class="name">LibreOffice</div>
												<div class="type" data-i18n="msstore.free">免费下载</div>
											</div>
											<div class="down">
												<div class="rating"></div>
												<div class="cate" data-i18n="msstore.business">办公</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="cnt apps"></div>
				<div class="cnt game"></div>
				<div class="cnt down"></div>
			</div>
		</div>
	</div>

	<div class="window setting">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/setting.svg" class="icon">
			<p data-i18n="setting.name">设置</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('setting')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('setting')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('setting')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/setting.svg" class="icon">
		</div>
		<div class="content" id="win-setting">
			<div class="menu">
				<a class="a user">
					<svg viewBox="0,0,257,344" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="user-clip1"><rect x="382" y="195" width="257" height="344" /></clipPath><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill3"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill4"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient></defs><g clip-path="url(#user-clip1)" transform="translate(-382 -195)"><path d="M637.755 433.872C642.215 515.221 579.577 537.983 508.011 537.983 436.444 537.983 376.676 507.833 383.513 437.11 383.109 425.234 389.59 414.133 398.634 409.891 413.82 402.768 444.753 402.936 507.484 402.997 570.214 403.058 609.164 402.279 621.521 407.947 633.878 413.614 638.011 424.609 637.755 433.872Z" fill="url(#user-fill3)" fill-rule="evenodd" /><path d="M422 285C422 235.847 461.623 196 510.5 196 559.377 196 599 235.847 599 285 599 334.153 559.377 374 510.5 374 461.623 374 422 334.153 422 285Z" fill="url(#user-fill4)" fill-rule="evenodd" /></g></svg>
					<div>
						<p data-i18n="setting.usrname">星瓶</p>
						<p><EMAIL></p>
					</div>
				</a>
				<input type="text" class="input" placeholder="查找设置" style="padding-left: 30px;" data-i18n-attr="placeholder" data-i18n-key="setting.sch">
				<input-before class="bi bi-search"></input-before>
				<list class="focs"><!--压缩行数-->
					<a class="enable home check" onclick="apps.setting.page('home')"><img src="apps/icons/setting/home.png"><span data-i18n="home">主页</span></a><!-- 主页  @lh11117 (2023/9/31~2023/10/1) -->
					<a class="enable system" onclick="apps.setting.page('system')"><img src="apps/icons/setting/system.png"><span data-i18n="setting.system">系统</span></a><!-- 系统  stsc 远古-->
					<a class="enable blueteeth" onclick="apps.setting.page('blueteeth')"><img src="apps/icons/setting/blueteeth.png"><span data-i18n="setting.device">蓝牙和其他设备</span></a><!-- 蓝牙与设备  @Junchen Yi 2023-9-9-->
					<a class="enable network" onclick="apps.setting.page('network')"><img src="apps/icons/setting/network.png"><span data-i18n="setting.network">网络和 Internet</span></a><!-- 网络和 Internet  @Junchen Yi 2023-9-10-->
					<a class="enable appearance avlb" onclick="apps.setting.page('appearance')"><img src="apps/icons/setting/personal.png"><span data-i18n="setting.psnl">个性化</span></a><!-- 个性化  stsc 远古-->
					<a class="enable apps" onclick="apps.setting.page('apps')"><img src="apps/icons/setting/apps.png"><span data-i18n="setting.apps">应用</span></a><!-- 应用  @Junchen Yi 2023-9-11-->
					<a class="enable user" onclick="apps.setting.page('user')"><img src="apps/icons/setting/user.png"><span data-i18n="setting.accounts">账户</span></a><!-- 账户  @Junchen Yi 2023-9-11-->
					<a class="enable time" onclick="apps.setting.page('time')"><img src="apps/icons/setting/time.png"><span data-i18n="setting.timelang">时间和语言</span></a><!-- 时间  @Junchen Yi 2023-9-17-->
					<a class="enable game" onclick="apps.setting.page('game')"><img src="apps/icons/setting/game.png"><span data-i18n="setting.game">游戏</span></a>
					<a><img src="apps/icons/setting/help.png"><span data-i18n="setting.acc">辅助功能</span></a>
					<a class="enable safe avlb" onclick="apps.setting.page('safe')"><img src="apps/icons/setting/safe.png"><span data-i18n="setting.privacy">隐私和安全性</span></a>
					<a class="enable update avlb" onclick="apps.setting.page('update')"><img src="apps/icons/setting/update.png"><span data-i18n="setting.update">Windows 更新</span></a><!-- 更新  stsc 远古-->
					<span class="focs"></span>
				</list>
			</div>
			<div class="page">
				<!-- 主页  begin -->
				<div class="cnt home show">
					<p class="title">主页</p>
					<div style="display: flex;margin: 25px;">
						<p class="s-icon" style="font-size: 70px;margin: -8px 10px 0px 20px;"></p>
						<div>
							<p style="font-size: 28px;text-overflow: ellipsis;white-space: nowrap;">Desktop-PingGai</p>
							<p style="color: #7f7f7f;margin:-8px 0 -5px 0;">瓶盖</p>
							<a style="color: var(--href);text-decoration: underline !important;;" class="a">重命名</a>
						</div>
					</div>
					<div class="setting-card">
						<p style="padding: 20px"><icon><img src="apps/icons/setting/ms.png" style="width: 35px; height: 35px;"></icon><br>所有功能尽在 Microsoft 账户<br><span style="font-size: 14px;color:var(--text2)">登录后，即可在设备上使用你最喜爱的 Microsoft 应用和服务。可以备份设备、让设备更安全，并使用 Microsoft 365 应用版和云储存空间。</span><br><img src="apps/icons/setting/ms365.png"></p>
						<div style="padding:  20px">
							<a class="a button" onclick="window.location.href='./bluescreen.html?code=YOU_CAN_NOT_SIGN_IN_YOUR_MICROSOFT_ACCOUNT'" win12_title="BSOD is coming!">登录</a>
						</div>
					</div>
					<div class="setting-card">
						<p style="padding: 20px">推荐设置<br><span style="font-size: 14px;color:var(--text2)">最近使用的和常用的设置</span></p>
						<div class="setting-list">
							<a><icon><i class="bi bi-power"></i></icon><div><p>电源和电池</p><p></p></div><i class="bi bi-chevron-right"></i></a>
							<a><icon><i class="bi bi-window-desktop"></i></icon><div><p>任务栏</p><p></p></div><i class="bi bi-chevron-right"></i></a>
							<a><icon><i class="bi bi-camera"></i></icon><div><p>摄像头</p><p></p></div><i class="bi bi-chevron-right"></i></a>
						</div>
					</div>
					<div class="setting-card">
						<p style="padding: 20px">蓝牙设备<br><span style="font-size: 14px;color:var(--text2)">管理、添加和删除设备</span></p>
						<div class="setting-list">
							<a onclick="$('#win-setting>.menu>list>a.blueteeth')[0].click();"><icon></icon><div><p>蓝牙</p><p></p></div><i class="bi bi-chevron-right"></i></a>
						</div>
					</div>
					<div class="setting-card">
						<p style="padding:20px">个性化设备</p>
						<div class="setting-list">
							<a onclick="$('#win-setting>.menu>list>a.appearance')[0].click();"><icon><i class="bi bi-palette"></i></icon><div><p>个性化</p><p></p></div><i class="bi bi-chevron-right"></i></a>
						</div>
					</div>
					<div style="height: 60px;"></div>
				</div>
				<!-- 主页  end -->
				<div class="cnt system">
					<p class="title">系统</p>
					<div style="display: flex;">
						<p class="s-icon" style="font-size: 70px;margin: -8px 10px 0px 20px;"></p>
						<div>
							<p style="font-size: 28px;text-overflow: ellipsis;white-space: nowrap;">Desktop-PingGai</p>
							<p style="color: #7f7f7f;margin:-8px 0 -5px 0;">瓶盖</p>
							<a style="color: var(--href);text-decoration: underline !important;;" class="a">重命名</a>
						</div>
					</div>
					<div class="setting-list">
						<a><icon></icon><div><p>显示</p><p>显示器、亮度、夜间模式、显示描述</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>声音</p><p>声音级别、输入、输出、声音设备</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>通知</p><p>来自系统和应用的警报</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>专注助手</p><p>通知、自动规则</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon class="b">&#xF186;</icon><div><p>电源</p><p>睡眠、电池使用情况、节电模式</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>存储</p><p>存储空间、驱动器、配置规则</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>多任务处理</p><p>贴靠窗口、桌面、任务切换</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>激活</p><p>激活状态、订阅、产品密钥</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>疑难解答</p><p>建议的疑难解答、首选项和历史记录</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>恢复</p><p>重置、高级启动、返回</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>投影到此电脑</p><p>权限、配对 PIN、可发现性</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>远程桌面</p><p>远程桌面用户、连接权限</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>剪贴板</p><p>剪贴和复制历史记录、同步、清除</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>关于</p><p>设备规格、重命名电脑、Windows 规格</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 蓝牙与设备  @Junchen Yi 2023-9-9-->
				<div class="cnt blueteeth">
					<p class="title">蓝牙与设备</p>
					<div style="display: flex;">
						<p class="s-icon" style="font-size: 70px;margin: -8px 10px 0px 20px;"></p>
						<div>
							<p style="color: #7f7f7f;margin:-8px 0 -5px 0;">我的蓝牙名称</p>
							<p style="font-size: 28px;text-overflow: ellipsis;white-space: nowrap;">Blueteeth-PingGai</p>
							<p class="blueteeth-tips-text" style="color: #7f7f7f;margin:-8px 0 -5px 0;">这是您在其他设备的蓝牙列表中的名称</p>
							<a style="color: var(--href);text-decoration: underline !important;;" class="a">重命名</a>
						</div>
					</div>
					<div class="setting-list">
						<a><icon></icon><div><p>连接</p><p>搜索新的蓝牙设备</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>设备</p><p>蓝牙鼠标、键盘、其他蓝牙设备</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>打印机与扫描仪</p><p>设置打印机、排除故障</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>手机</p><p>访问Android设备的照片、文件等</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>触控板</p><p>点击、滚动、缩放、手势</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>触控笔与触摸</p><p>设置触控笔、设置触控屏幕</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>USB设备</p><p>通过USB连接更多设备...</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 蓝牙与设备 end -->
				<!-- 网络和 Internet  @Junchen Yi 2023-9-10-->
				<div class="cnt network">
					<p class="title">网络和 Internet</p>
					<div style="display: flex;">
						<p class="s-icon" style="font-size: 70px;margin: -8px 15px 0px 20px;"></p>
						<div>
							<p style="color: #7f7f7f;margin:-8px 0 -5px 0;">Wifi 已连接</p>
							<p style="font-size: 28px;text-overflow: ellipsis;white-space: nowrap;">PingGai - 5G</p>
							<a style="color: var(--href);text-decoration: underline !important;;" class="a">断开</a>
						</div>
					</div>
					<div class="setting-list">
						<a><icon></icon><div><p>无线局域网</p><p>连接、管理已连接网络</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>移动网络 未连接</p><p>通过移动网络访问互联网</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>VPN</p><p>添加、连接、管理</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>移动互联网</p><p>通过本机移动网络共享互联网</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>飞行模式</p><p>暂停所有通讯与设备连接</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>代理</p><p>用于 Wifi 和以太网的代理服务器</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>拨号</p><p>通过 Wifi 网络拨打电话</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>高级设置</p><p>查看所有网络适配器、网络重置</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 网络和 Internet end  @Junchen Yi -->
				<div class="cnt appearance">
					<p class="title" data-i18n="setting.psnl">个性化</p>
					<div class="setting-list" style="margin-top: 10px;">
						<a class="dp app-color" onclick="$('.dp.app-color').toggleClass('show');">
							<icon></icon>
							<div>
								<p data-i18n="setting.psnl.color">颜色</p>
								<p data-i18n="setting.psnl.color-dt">设置 Windows 的主题色</p>
							</div><i class="bi bi-chevron-down"></i>
						</a>
						<div class="dp app-color">
							<p data-i18n="setting.psnl.color.now">当前颜色</p>
							<div class="color"
								style="background: linear-gradient(120deg,var(--theme-1),var(--theme-2));"></div>
							<br />
							<p data-i18n="setting.psnl.color.custom">自定义颜色</p>
							<div style="display: flex;">
								<div class="a act color theme1" style="background: #47acff"
									onclick="$('div.app-color>div>input.theme1inp')[0].click();"></div>
								<input type="color" class="theme1inp"
									onchange="$('div.app-color>div>.color.theme1').css('background-color',this.value);"
									value="#47acff">
								<div
									style="width: 10px;height: 1px;background-color: var(--text);margin: 17px 5px 0 10px;">
								</div>
								<div class="a act color theme2" style="background: #1168ae"
									onclick="$('div.app-color>div>input.theme2inp')[0].click();"></div>
								<input type="color" class="theme2inp"
									onchange="$('div.app-color>div>.color.theme2').css('background-color',this.value);"
									value="#1168ae">
								<a class="a button" style="margin-left: 20px;" onclick="$(':root').css('--theme-1',$('div.app-color>div>input.theme1inp').val());
									$(':root').css('--theme-2',$('div.app-color>div>input.theme2inp').val());setData('color1',$('div.app-color>div>input.theme1inp').val());
									setData('color2',$('div.app-color>div>input.theme2inp').val())" data-i18n="apply">应用</a>
							</div>
						</div>
						<a class="dp theme" onclick="$('.dp.theme').toggleClass('show');if (!($('#set-theme>.a').length)) apps.setting.theme_get();">
							<icon>&#xE771;</icon><div><p data-i18n="setting.psnl.theme">主题</p><p data-i18n="setting.psnl.theme-dt">(！消耗大量 github api 限度！) 设置 Windows 的主题 想要<span class="a jump" win12_title="https://github.com/tjy-gitnub/win12-theme" onclick="window.open('https://github.com/tjy-gitnub/win12-theme','_blank')">上传自己的主题</span>?</p></div><i class="bi bi-chevron-down"></i></a>
						<div class="dp theme" id="set-theme">
							<loading>
								<svg width="30px" height="30px" viewBox="0 0 16 16">
									<circle cx="8px" cy="8px" r="7px"
										style="stroke:#7f7f7f50;fill:none;stroke-width:3px;"></circle>
									<circle cx="8px" cy="8px" r="7px" style="stroke:#2983cc;stroke-width:3px;"></circle>
								</svg>
							</loading>
						</div>
						<div class="border-radius"><icon></icon><div><p data-i18n="setting.psnl.round">圆角</p><p data-i18n="setting.psnl.round-dt">设置系统界面中是否启用圆角</p></div>
							<div class="alr">
								<a class="a checkbox checked" id="sys_setting_1" onclick="$(this).toggleClass('checked');$(':root').toggleClass('nobr');sys_setting[0]=1-sys_setting[0];saveDesktop();"></a>
							</div>
						</div>
						<div class="border-radius"><icon></icon><div><p data-i18n="setting.psnl.animation">动画</p><p data-i18n="setting.psnl.animation-dt">系统界面元素过渡动画</p></div>
							<div class="alr">
								<a class="a checkbox checked" id="sys_setting_2" onclick="$(this).toggleClass('checked');$(':root').toggleClass('notrans');sys_setting[1]=1-sys_setting[1];saveDesktop();"></a>
							</div>
						</div>
						<div class="border-radius"><icon></icon><div><p data-i18n="setting.psnl.shadow">阴影</p><p data-i18n="setting.psnl.shadow-dt">为系统界面元素添加阴影效果</p></div>
							<div class="alr">
								<a class="a checkbox checked" id="sys_setting_3" onclick="$(this).toggleClass('checked');$(':root').toggleClass('nosd');sys_setting[2]=1-sys_setting[2];saveDesktop();"></a>
							</div>
						</div>
						<div class="border-radius"><icon></icon><div><p data-i18n="setting.psnl.alltransp">多窗口透明</p><p data-i18n="setting.psnl.alltransp-dt">为所有窗口开启透明效果，而不是仅用于焦点窗口 <span class="a jump" win12_title="开启后将占用大量GPU,可能造成卡顿">详细</span></p></div>
							<div class="alr">
								<a class="a checkbox" id="sys_setting_4" onclick="$(this).toggleClass('checked');$(':root').toggleClass('moreblur');sys_setting[3]=1-sys_setting[3];saveDesktop();"></a>
							</div>
						</div>
						<div class="border-radius"><icon>&#xE621;</icon><div><p data-i18n="setting.psnl.mica">Mica效果 (实验)</p><p data-i18n="setting.psnl.mica-dt">为焦点窗口开启mica效果 <a class="a jump" onclick="shownotice('feedback')">反馈</a></p></div>
							<div class="alr">
								<a class="a checkbox" id="sys_setting_5" onclick="$(this).toggleClass('checked');$(':root').toggleClass('mica');sys_setting[4]=1-sys_setting[4];saveDesktop();"></a>
							</div>
						</div>
						<div class="border-radius"><icon><i class="bi bi-music-note-beamed"></i></icon><div><p data-i18n="setting.psnl.startup-sound">开机音乐</p><p data-i18n="setting.psnl.startup-sound-dt">是否启用开机音乐</p></div>
							<div class="alr">
								<a class="a checkbox checked" id="sys_setting_6" onclick="sys_setting[5]=1-sys_setting[5];document.getElementById('sys_setting_6').setAttribute('class', 'a checkbox' + (sys_setting[5]?' checked':''));saveDesktop();"></a>
							</div>
						</div>
            <div class="border-radius"><icon><i class="bi bi-mic-fill"></i></icon><div><p data-i18n="setting.psnl.ball">语音输入球</p><p data-i18n="setting.psnl.ball-dt">是否启用语音输入球</p></div>
							<div class="alr">
								<a class="a checkbox checked" id="sys_setting_7" onclick="sys_setting[6]=1-sys_setting[6];document.querySelector('.rainbow-container-main').setAttribute('style', 'display:' + (sys_setting[6] ? 'block' : 'none')+ ';');document.getElementById('sys_setting_7').setAttribute('class', 'a checkbox' + (sys_setting[6]?' checked':''));updateVoiceBallStatus();saveDesktop();"></a>
							</div>
						</div>
					</div>
				</div>
				<!-- 应用  @Junchen Yi 2023-9-11-->
				<div class="cnt apps">
					<p class="title">应用</p>
					<div class="setting-list">
						<a><icon></icon><div><p>应用程序和功能</p><p>已安装的应用程序、应用程序执行别名</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>默认应用程序</p><p>文件和链接类型的默认值，其他默认值</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>离线地图</p><p>下载、存储位置、地图更新</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>可选内容</p><p>为您的设备提供额外功能</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>网站应用程序</p><p>可以在应用程序而不是浏览器中打开的网站</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>视频</p><p>视频格式调整、HDR 流媒体、电池选项</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>启动项</p><p>登录时自动启动的应用程序</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 应用 end  @Junchen Yi -->
				<!-- 账户  @Junchen Yi 2023-9-11-->
				<div class="cnt user">
					<p class="title">账户</p>
					<div style="display: flex;">
						<a class="a user" style="display:flex">
							<svg viewBox="0,0,257,344" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 100px;height: 100px;background-color: var(--hover-b);/* box-shadow: 0 0 10px var(--sd); */width: 60px;width: 60px;height: 60px;border-radius: 50%;padding: 20px;margin-right: 15px;margin-bottom: 15px;" overflow="hidden"><defs><clipPath id="user-clip1"><rect x="382" y="195" width="257" height="344" /></clipPath><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill3"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient><linearGradient x1="351.462" y1="233.56" x2="669.496" y2="500.422" gradientUnits="userSpaceOnUse" spreadMethod="reflect" id="user-fill4"><stop offset="0" stop-color="#A964C8" /><stop offset="0.35" stop-color="#A964C8" /><stop offset="0.87" stop-color="#2D8AD5" /><stop offset="1" stop-color="#2D8AD5" /></linearGradient></defs><g clip-path="url(#user-clip1)" transform="translate(-382 -195)"><path d="M637.755 433.872C642.215 515.221 579.577 537.983 508.011 537.983 436.444 537.983 376.676 507.833 383.513 437.11 383.109 425.234 389.59 414.133 398.634 409.891 413.82 402.768 444.753 402.936 507.484 402.997 570.214 403.058 609.164 402.279 621.521 407.947 633.878 413.614 638.011 424.609 637.755 433.872Z" fill="url(#user-fill3)" fill-rule="evenodd" /><path d="M422 285C422 235.847 461.623 196 510.5 196 559.377 196 599 235.847 599 285 599 334.153 559.377 374 510.5 374 461.623 374 422 334.153 422 285Z" fill="url(#user-fill4)" fill-rule="evenodd" /></g></svg>
							<div style="margin-top: 5px;">
								<p>星瓶</p>
								<p><EMAIL></p>
							</div>
						</a>
					</div>
					<div class="setting-list">
						<a><icon></icon><div><p>您的微软账户</p><p>订阅、账单等</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>您的账户信息</p><p>电子邮件、日历和联系人使用的帐户</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>登录选项</p><p>Windows Hello、安全密钥、密码、动态锁</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>电子邮件和账户</p><p>电子邮件、日历和联系人使用的帐户</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>家庭</p><p>管理您的家庭群组、编辑帐户类型和设备权限</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>Windows 备份</p><p>备份您的文件、应用程序、首选项以跨设备恢复它们</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>其他用户</p><p>设备访问、工作或学校用户、信息亭分配的访问</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>访问工作或学校账户</p><p>电子邮件、应用程序和网络等组织资源</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 账户 end -->
				<!-- 时间  @Junchen Yi 2023-9-17-->
				<div class="cnt time">
					<p class="title">时间和语言</p>
					<div class="settingTimeItem">
						<p class="settingTimeTitle">当前设备时间：</p>
						<span class="settingTime"></span>
					</div>
					<div class="setting-list">
						<a><icon></icon><div><p>日期与时间</p><p>时区、自动时钟设置、日历显示</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>语言和地区</p><p>Windows 和某些应用程序根据您所在的地区设置日期和时间的格式</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>输入</p><p>触摸键盘、文本建议、首选项</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon></icon><div><p>朗读与音频输入</p><p>语音语言、语音识别麦克风设置、声音</p></div><i class="bi bi-chevron-right"></i></a>
						<!-- <a class="selectLanguageA"><icon></icon><div><p>Windows 显示语言</p><p>设置和文件资源管理器等 Windows 功能将以这种语言显示</p></div>
							<select class="languageSelect bi-chevron-right">
								<option value="en">English</option>
								<option value="es">Español (Spanish)</option>
								<option value="fr">Français (French)</option>
								<option value="de">Deutsch (German)</option>
								<option value="zh" selected>中文 (Chinese)</option>
								<option value="ja">日本語 (Japanese)</option>
								<option value="ko">한국어 (Korean)</option>
								<option value="ar">العربية (Arabic)</option>
								<option value="ru">Русский (Russian)</option>
								<option value="pt">Português (Portuguese)</option>
								<option value="it">Italiano (Italian)</option>
								<option value="nl">Nederlands (Dutch)</option>
								<option value="hi">हिन्दी (Hindi)</option>
								<option value="sv">Svenska (Swedish)</option>
								<option value="fi">Suomi (Finnish)</option>
								<option value="da">Dansk (Danish)</option>
								<option value="no">Norsk (Norwegian)</option>
								<option value="tr">Türkçe (Turkish)</option>
								<option value="pl">Polski (Polish)</option>
								<option value="th">ภาษาไทย (Thai)</option>
							</select>
						</a> -->
						<!-- 真的不支持这么多语言的qwq  from stsc -->
					</div>
				</div>
				<!-- 时间 end -->
				<!-- 游戏 begin -->
				<div class="cnt game">
					<p class="title">游戏</p>
					<div class="setting-list">
						<a><icon><i class="bi bi-xbox"></i></icon><div><p>Xbox Game Bar</p><p>控制器和键盘快捷方式</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon><i class="bi bi-camera"></i></icon><div><p>摄像</p><p>保存位置，录制首选项</p></div><i class="bi bi-chevron-right"></i></a>
						<a><icon><i class="bi bi-controller"></i></icon><div><p>游戏模式</p><p>优化电脑以便畅玩</p></div><i class="bi bi-chevron-right"></i></a>
					</div>
				</div>
				<!-- 游戏 end -->
				<div class="cnt safe">
					<p class="title">隐私和安全性</p>
					<div class="setting-list">
						<a onclick="localStorage.setItem('desktop','[]');setIcon();">
							<icon><i class="bi bi-trash3"></i></icon>
							<div>
								<p>清除桌面图标</p>
								<p>这将清除用户自定义的所有图标</p>
							</div><i class="bi bi-chevron-right"></i>
						</a>
						<a onclick="$('.dp.access-token').toggleClass('show')" class="dp access-token">
							<icon><i class="bi bi-key"></i></icon>
							<div>
								<p>Token 管理</p>
								<p>添加或删除 GitHub Access Token</p>
							</div>
							<i class="bi bi-chevron-down"></i>
						</a>
						<div class="dp access-token">
							<input type="text" class="token input">
							<div class="container" style="display: flex; align-items: center; gap: 10px; justify-content: space-evenly; margin-top: 6px;">
								<div class="button" onclick="localStorage.setItem('token', $('.dp.access-token>input.token').val());">保存 Token</div>
								<div class="button" onclick="localStorage.setItem('token', '');">清除 Token</div>
								<div class="button" onclick="$('.dp.access-token>input.token').val(localStorage.getItem('token'));">查看 Token</div>
							</div>
							<div class="detail" style="font-size: 0.8em; color: var(--text2);">您可以在这里填入您的Github Access Token来提高Github API限制访问次数，即可以更好地使用 Windows 更新、主题等功能。您的 Token 仅会存放在本地浏览器中，该数据不会传输至服务端，也更不会外泄，但请您妥善保管好自己的 Token。</div>
						</div>
					</div>
				</div>
				<div class="cnt update">
					<p class="title">Windows 更新</p>
					<div class="lo">
						<svg viewBox="0 0 65 65" version="1.1" id="svg5" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg"><defs id="defs2"><linearGradient id="linearGradient12966"><stop style="stop-color:#1946ff;stop-opacity:1;"offset="0"id="stop12962"/><stop style="stop-color:#3eddff;stop-opacity:1;"offset="1"id="stop12964"/></linearGradient><linearGradient id="linearGradient10659"><stop style="stop-color:#6c00e8;stop-opacity:1;"offset="0"id="stop10655"/><stop style="stop-color:#d05ed6;stop-opacity:1;"offset="1"id="stop10657"/></linearGradient><linearGradient xlink:href="#linearGradient10659"id="linearGradient10661"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"gradientUnits="userSpaceOnUse"/><linearGradient xlink:href="#linearGradient10659"id="linearGradient12869"gradientUnits="userSpaceOnUse"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"/><linearGradient xlink:href="#linearGradient10659"id="linearGradient12871"gradientUnits="userSpaceOnUse"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"/><linearGradient xlink:href="#linearGradient12966"id="linearGradient12968"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"gradientUnits="userSpaceOnUse"/><linearGradient xlink:href="#linearGradient12966"id="linearGradient13788"gradientUnits="userSpaceOnUse"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"/><linearGradient xlink:href="#linearGradient12966"id="linearGradient13790"gradientUnits="userSpaceOnUse"x1="40.558044"y1="64.876854"x2="103.16348"y2="64.876854"/></defs><g id="layer1"><g id="g8420"transform="rotate(-173.25016,50.599831,57.584909)"style="stroke:url(#linearGradient10661)"><path style="fill:none;stroke:url(#linearGradient12869);stroke-width:5;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none"id="path234-2"d="M 43.058046,79.121117 A 28.487005,28.487005 0 0 1 66.602472,51.066164 28.487005,28.487005 0 0 1 98.316952,69.38588"/><path style="fill:none;stroke:url(#linearGradient12871);stroke-width:5;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none"d="M 85.756154,73.849626 100.66351,72.185133 98.999027,57.27777"id="path1617-6"/></g><g id="g8420-2"transform="rotate(6.74984,459.81282,-281.39813)"style="stroke:url(#linearGradient12968);stroke-opacity:1"><path style="fill:none;stroke:url(#linearGradient13788);stroke-width:5;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none;stroke-opacity:1"id="path234-2-2"d="M 43.058046,79.121117 A 28.487005,28.487005 0 0 1 66.602472,51.066164 28.487005,28.487005 0 0 1 98.316952,69.38588"/><path style="fill:none;stroke:url(#linearGradient13790);stroke-width:5;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:none;stroke-opacity:1"d="M 85.756154,73.849626 100.66351,72.185133 98.999027,57.27777"id="path1617-6-0"/></g></g></svg>
						<div class="update-main">
							<div class="part">
								<p class="notice">正在检查更新...</p>
								<p class="detail"></p>
							</div>
							<div><a class="a button" style="font-size: 16px;margin-top: -7.5px;" onclick="apps.setting.checkUpdate()">检查更新</a></div>
						</div>
					</div>
					<div class="setting-list">
						<div class="update-now"><icon>&#xE794;</icon><div><p>正在检查更新...</p><p></p></div><div class="alr"><a class="a button" style="font-size: 16px;margin-top: -7.5px;" onclick="sendToSw({ head: 'update' }); localStorage.setItem('update', true); shownotice('setting.update')">更新</a></div></div>
						<a onclick="openapp('about');$('#win-about>.update').addClass('show');$('#win-about>.about').removeClass('show');"><icon>&#xE946;</icon><div><p>历史版本</p><p>查看历史版本功能</p></div><i class="bi bi-chevron-right"></i></a>
						<a onclick="sendToSw({ head: 'update',force:true }); shownotice('setting.down')"><icon>&#xE896;</icon><div><p>下载完整内容</p><p>当更新或系统出现问题，使用此项下载最新完整内容</p></div><i class="bi bi-chevron-right"></i></a>
						<div><icon></icon><div><p>自动更新</p><p>设置是否启用自动更新</p></div>
							<div class="alr">
								<a class="checkbox checked a" onclick="$(this).toggleClass('checked'); autoUpdate = !autoUpdate; localStorage.setItem('autoUpdate', autoUpdate)"></a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="window explorer tabs">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/explorer.svg" class="icon">
			<!-- <p style="width:min-content">文件资源管理器</p> -->
			<div class="tabs">
			</div>
			<div>
				<a class="a wbtg red" onclick="hidewin('explorer')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('explorer')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('explorer')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/explorer.svg" class="icon">
		</div>
		<div class="content" id="win-explorer">
			<div class="path">
				<a class="a btn btn-icon back" onclick="apps.explorer.back(apps.explorer.tabs[apps.explorer.now][0])"><i class="bi bi-arrow-left"></i></a>
				<a class="a btn btn-icon front" onclick="apps.explorer.front(apps.explorer.tabs[apps.explorer.now][0])"><i class="bi bi-arrow-right"></i></a>
				<a class="a btn btn-icon goback"><i class="bi bi-arrow-up"></i></a>
				<p class="tit"></p>
				<div class="search">
					<input type="text" class="input" placeholder="在这里输入你要搜索的内容" data-i18n-attr="placeholder" data-i18n-key="sch-ph">
					<input-before class="bi bi-search"></input-before>
				</div>
			</div>
			<div class="page">
				<div class="menu">
					<div class="card pinned">
						<p class="title"><span style="font: 15px;">📌</span> <span data-i18n="explorer.pinned">已固定</span></p>
						<list>
							<a><img src="apps/icons/explorer/qa.png"><span data-i18n="explorer.quickacc">快速访问</span></a>
							<a><img src="apps/icons/explorer/od.png">OneDrive</a>
							<a class="check" onclick="apps.explorer.reset();"><span
									style="background:linear-gradient(180deg, var(--theme-1), var(--theme-2));width:4px;height: 19px;border-radius: 10px;margin-left: -12px;margin-right: 8px;margin-top: 1px;"></span>
								<img src="apps/icons/explorer/thispc.svg"><span data-i18n="explorer.thispc">此电脑</span></a>
							<a><img src="apps/icons/explorer/rb.png"><span data-i18n="explorer.bin">回收站</span></a>
						</list>
					</div>
					<div class="card tags">
						<p class="title"><span style="font: 15px;">🏷</span> <span data-i18n="explorer.tag">标签</span></p>
						<list>
							<a><span style="background-color: red;"></span><span data-i18n="explorer.tag.red">红色</span></a>
							<a><span style="background-color: #3981d9;"></span><span data-i18n="explorer.tag.blue">蓝色</span></a>
							<a><span style="background-color: yellow;"></span><span data-i18n="explorer.tag.yellow">黄色</span></a>
							<a><span style="background-color: green;"></span><span data-i18n="explorer.tag.green">绿色</span></a>
							<a><span style="background-color: #fc9816;"></span><span data-i18n="explorer.tag.orange">橙色</span></a>
							<a><span style="background-color: purple;"></span><span data-i18n="explorer.tag.purple">紫色</span></a>
							<a><span style="background-color: #ffcad4;"></span><span data-i18n="explorer.tag.pink">粉色</span></a>
						</list>
					</div>
				</div>
				<div class="main">
					<div class="tool micaalt">
						<a class="a b t act"
							onclick="apps.explorer.add($('#win-explorer>.path>.tit')[0].dataset.path,'新建文本文档.txt')"><img
								src="apps/icons/explorer/tool-new.png"><span data-i18n="explorer.new">新建</span></a>
						<div class="hr"></div>
						<a class="a b act"><img src="apps/icons/explorer/tool-cut.png"></a>
						<a class="a b act"><img src="apps/icons/explorer/tool-copy.png"></a>
						<a class="a b act"><img src="apps/icons/explorer/tool-paste.png"></a>
						<a class="a b act"><img src="apps/icons/explorer/tool-rename.png"></a>
						<div class="hr"></div>
						<a class="a b t act"><img src="apps/icons/explorer/tool-sort.png"><span data-i18n="explorer.sort">排序方式</span></a>
						<a class="a b t act"><img src="apps/icons/explorer/tool-view.png"><span data-i18n="explorer.view">布局</span></a>
					</div>
					<div class="content" onclick="apps.explorer.del_select()"
						oncontextmenu="return showcm(event,'explorer.content',null);">
						<div class="view">
							<!-- 文件列表  -->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="window calc">
		<div class="titbar">
			<img src="icon/calc.svg" class="icon">
			<p data-i18n="calc.name">计算器</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('calc')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" style="pointer-events: none;color: #777;"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('calc')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/calc.svg" class="icon">
		</div>
		<div class="content" id="win-calc">
			<div class="container">
			<input id="calc-input" readonly="true" value="0" onkeydown="
				switch (event.key) {
					case '+':
						appCalculator.func_key(1);
						appCalculator.check($('#win-calc>.keyb>.jia')[0]);
						break;
					case '-':
						appCalculator.func_key(2);
						appCalculator.check($('#win-calc>.keyb>.jian')[0]);
						break;
					case '*':
						appCalculator.func_key(3);
						appCalculator.check($('#win-calc>.keyb>.cheng')[0]);
						break;
					case '/':
						appCalculator.func_key(4);
						appCalculator.check($('#win-calc>.keyb>.chu')[0]);
						break;
					case '=':
						appCalculator.eq();
						break;
					case 'Enter':
						appCalculator.eq();
						break;
					case 'Backspace':
						appCalculator.backspace();
						break;
					case '.':
						appCalculator.point();
						break;
				}
				if (!isNaN(event.key)) {
					appCalculator.number_key(Number(event.key));
				}
			"></input>
			</div>
			
			<div class="keyb">
				<a class="a b" onclick="appCalculator.square('calc-input')">𝑥²</a>
				<a class="a b" onclick="appCalculator.squareRoot('calc-input')">√𝑥</a>
				<a class="a b" onclick="appCalculator.clear_num('calc-input')">C</a>
				<a class="a b u jia" onclick="appCalculator.func_key(1); appCalculator.check(this);">+</a>
				<a class="a b" onclick="appCalculator.number_key(7);">7</a>
				<a class="a b" onclick="appCalculator.number_key(8);">8</a>
				<a class="a b" onclick="appCalculator.number_key(9);">9</a>
				<a class="a b u jian" onclick="appCalculator.func_key(2); appCalculator.check(this);">-</a>
				<a class="a b" onclick="appCalculator.number_key(4);">4</a>
				<a class="a b" onclick="appCalculator.number_key(5);">5</a>
				<a class="a b" onclick="appCalculator.number_key(6);">6</a>
				<a class="a b u cheng" onclick="appCalculator.func_key(3); appCalculator.check(this);">×</a>
				<a class="a b" onclick="appCalculator.number_key(1);">1</a>
				<a class="a b" onclick="appCalculator.number_key(2);">2</a>
				<a class="a b" onclick="appCalculator.number_key(3);">3</a>
				<a class="a b u chu" onclick="appCalculator.func_key(4); appCalculator.check(this);">÷</a>
				<a class="a b" onclick="appCalculator.point()">.</a>
				<a class="a b" onclick="appCalculator.number_key(0);">0</a>
				<a class="a b" onclick="appCalculator.backspace('calc-input')"><i class="bi bi-backspace"></i></a>
				<a class="a b ans u" onclick="if(!appCalculator.eq('calc-input')){ appCalculator.clear_num(); shownotice('ZeroDivision')}">=</a>
			</div>
		</div>
	</div>
	<div class="window about">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/about.svg" class="icon">
			<p data-i18n="about.name">关于 Win12 网页版</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('about')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('about')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('about')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/about.svg" class="icon">
		</div>
		<div class="content" id="win-about">
			<div class="menu">
				<list class="focs">
					<a onclick="$('#win-about>.about').addClass('show');$('#win-about>.update').removeClass('show');"><img src="apps/icons/about/info.svg" height="17px" width="19px"><span data-i18n="about.intro">简介</span></a>
					<a onclick="$('#win-about>.update').addClass('show');$('#win-about>.about').removeClass('show');"><img src="apps/icons/about/update.svg" height="20px" width="18px"><span data-i18n="about.update">更新记录</span></a>
				</list>
			</div>
			<div class="cnt about">
				<h1 class="tit" data-i18n="about.intro.name"><span></span>Windows 12 网页版</h1>
				<div style="margin-left: 20px;">
					<h2 class="tit" data-i18n="about.intro.intro"><span></span>简介</h2>
					<p data-i18n="about.intro.intro.p1">&emsp;&emsp;Windows 12 网页版是一个开源项目，由星源原创，使用 HTML、CSS 和 JavaScript，在网络上模拟、创新操作系统。</p>
					<p data-i18n="about.intro.intro.p2">&emsp;&emsp;此项目已发布至 GitHub，<a onclick="window.open('https://github.com/tjy-gitnub/win12','_blank');" win12_title="https://github.com/tjy-gitnub/win12" class="jump">点击此处查看</a>。</p>
					<p data-i18n="about.intro.intro.p3">&emsp;&emsp;若您对于该项目有任何意见或建议，请在 GitHub 上<a onclick="window.open('https://github.com/tjy-gitnub/win12/issues','_blank');" win12_title="https://github.com/tjy-gitnub/win12/issues" class="jump">提交 issues</a>，您的问题会被尽可能快地解决。</p>
					<p data-i18n="about.intro.intro.p4">&emsp;&emsp;原创作者星源，点击以<a onclick="window.open('https://tjy-gitnub.github.io/','_blank');" win12_title="https://tjy-gitnub.github.io/" class="jump">了解详细</a>。</p>
					<h2 class="tit" data-i18n="about.intro.os"><span></span>开源说明</h2>
					<p data-i18n="about.intro.os.p1">&emsp;&emsp;此项目是一个开源项目。此项目使用 EPL v2.0 开源许可。开源许可是具有法律效力的合同，请自觉遵守开源许可，尊重他人劳动。</p>
					<p data-i18n="about.intro.os.p2">&emsp;&emsp;根据许可，你可以对该项目进行传播、分发、修改以及二次发布，包括个人和商业用途。</p>
					<p data-i18n="about.intro.os.p3">&emsp;&emsp;但您必须给出源码来源，<strong>包括作者，项目链接等</strong>，必须使用相同的协议开源。</p>
					<p data-i18n="about.intro.os.p4">&emsp;&emsp;不是在该项目基础上进行增加、修改的，仅参考源码的，不需要开源，但也仅供学习用途。</p>
					<h2 class="tit" data-i18n="about.intro.thank"><span></span>特别感谢</h2>
					<p data-i18n="about.intro.thank.p1">&emsp;&emsp;特别感谢 @NB-Group，他为本项目的后端开发做出了重要贡献！</p>
					<h2 class="tit" data-i18n="about.intro.contri"><span></span>贡献者</h2>
					<p data-i18n="about.intro.contri.p1">&emsp;&emsp;感谢所有项目的贡献者（实时数据，计算方法：即为 Commits 次数）</p>
					<div id="contri">
						<loading>
							<svg width="30px" height="30px" viewBox="0 0 16 16">
								<circle cx="8px" cy="8px" r="7px" style="stroke:#7f7f7f50;fill:none;stroke-width:3px;">
								</circle>
								<circle cx="8px" cy="8px" r="7px" style="stroke:#2983cc;stroke-width:3px;"></circle>
							</svg>
						</loading>
					</div>
					<h2 class="tit"><span></span>Star</h2>
					<p data-i18n="about.intro.star.p1">&emsp;&emsp;感谢<a class="jump" win12_title="给个 Star 好不好？(即将跳转到：https://github.com/tjy-gitnub/win12/stargazers)" onclick="window.open('https://github.com/tjy-gitnub/win12/stargazers','_blank')">所有支持我们的人</a>。</p>
					<div id="StarShow">
						<loading>
							<svg width="30px" height="30px" viewBox="0 0 16 16">
								<circle cx="8px" cy="8px" r="7px" style="stroke:#7f7f7f50;fill:none;stroke-width:3px;">
								</circle>
								<circle cx="8px" cy="8px" r="7px" style="stroke:#2983cc;stroke-width:3px;"></circle>
							</svg>
						</loading>
					</div>
					<h2 class="tit" data-i18n="about.intro.others"><span></span>其它</h2>
					<p data-i18n="about.intro.others.p1">&emsp;&emsp;此项目基于目前 Windows 版本创造，与微软的 Windows 12 正式版本不一致。</p>
					<p data-i18n="about.intro.others.p2">&emsp;&emsp;此项目绝不附属于微软，且不应与微软操作系统或产品混淆，这也不是 Windows365 cloud PC。</p>
					<p data-i18n="about.intro.others.p3">&emsp;&emsp;本项目中微软、Windows 和其他示范产品是微软公司的商标。本项目中 Android 是谷歌公司的商标。</p>
				</div>
			</div>
			<div class="cnt update">
				<h1 class="tit" data-i18n="about.update.name"><span></span>更新记录</h1>
				<div style="margin-left: 20px;">
					<details><summary><span>v10.0.4</span>
					</v10></span> 扫雷</summary><p>
						&emsp;&emsp;-(更新来自 @kjmjh)<br>
				        &emsp;&emsp;-更新了扫雷<br>
				    </p></details>
					<details><summary><span>v10.0.3</span> 终端彩蛋命令和系统工具</summary><p>
						&emsp;&emsp;-添加新的终端命令：cls、help、hello、matrix、snow、dance、systeminfo 和 starwars<br>
						&emsp;&emsp;-增加互动和趣味彩蛋效果，如黑客帝国代码雨、下雪、窗口跳舞和星球大战文字滚动<br>
						&emsp;&emsp;-添加系统信息显示和基础终端实用工具<br>
						&emsp;&emsp;-增强终端用户体验，添加趣味和实用命令<br>
					</p></details>
					<details><summary><span>v10.0.2</span> 细节优化和修复</summary><p>
						&emsp;&emsp;-(更新来自 @tjy-gitnub)<br>
						&emsp;&emsp;-Copilot 每日对话限制<br>
						&emsp;&emsp;-细节优化和修复<br>
					</p></details>
					<details><summary><span>v10.0.1</span> 繁體中文語言支持</summary><p>
						&emsp;&emsp;-(更新来自 @tjy-gitnub)<br>
						&emsp;&emsp;-添加繁中語言支持<br>
						&emsp;&emsp;-细节优化和修复<br>
						&emsp;&emsp;无偿请求帮助我们翻译此项目！详见<a class="jump" href="https://github.com/tjy-gitnub/win12">项目主页</a><br>
					</p></details>
					<details><summary><span>v10.0.0</span> AI Copilot 回归，走向国际化</summary><p>
						&emsp;&emsp;-(更新来自 @NB-Group)<br>
						&emsp;&emsp;重启AI Copilot！<br>
						&emsp;&emsp;-(更新来自 @tjy-gitnub)<br>
						&emsp;&emsp;-部分英语翻译<br>
						&emsp;&emsp;-细节优化和修复<br>
					</p></details>
					<details><summary><span>v9.2.1</span> 修复一些问题</summary><p>
						&emsp;&emsp;-(更新来自 @tjy-gitnub)<br>
						&emsp;&emsp;-拆解 desktop.js<br>
						&emsp;&emsp;-细节优化和修复<br>
					</p></details>
					<details><summary><span>v9.2.0</span> 整理</summary><p>
						&emsp;&emsp;-(更新来自 @tjy-gitnub)<br>
						&emsp;&emsp;修改大量积累的 bug<br>
						&emsp;&emsp;-关于开始菜单全屏的问题<br>
						&emsp;&emsp;-关于小组件的大量问题<br>
						&emsp;&emsp;-标签页的移动端适配问题<br>
						&emsp;&emsp;-天气小组件 api 失效的问题<br>
						&emsp;&emsp;新增和完善部分内容<br>
						&emsp;&emsp;-鼠标中键关闭标签页<br>
						&emsp;&emsp;-登录界面的美化<br>
						&emsp;&emsp;-细节优化和修复<br>
					</p></details>
					<details><summary><span>v9.1.2</span> 任务栏预览</summary><p>
						&emsp;&emsp;-(更新来自 @fzlzjerry)<br>
						&emsp;&emsp;-加入任务栏窗口预览<br>
					</p></details>
					<details><summary><span>v9.1.1</span> 移动端适配</summary><p>
						&emsp;&emsp;-(更新来自@NB-Group)<br>
						&emsp;&emsp;- 在移动端竖屏体验时将会提示“横屏以获得最佳体验”<br>
					</p></details>
					<details><summary><span>v9.1.0</span> 真正的在线新闻功能！</summary><p>
						&emsp;&emsp;-(更新来自@lingbopro)<br>
						&emsp;&emsp;- 现在在小组件面板里有真正从网络实时获取的新闻！ <br>
						&emsp;&emsp;&nbsp; （可能会出现卡顿，触发方式薛定谔）<br>
						&emsp;&emsp;- 顺便重构了一下新闻部分</p>
					</p></details>
					<details><summary><span>v9.0.0</span> 窗口背景材质超级大升级！</summary><p>
						&emsp;&emsp;-(更新来自@NB-Group)<br>
						&emsp;&emsp;- 新材质(仿win11 Mica)非常高级！ <br>
						&emsp;&emsp;- 将设置的UI更新了</p>
					</p></details>
					<details><summary><span>v8.1.0</span> 整理</summary><p>
						&emsp;&emsp;-(更新来自@tjy-gitnub)<br>
						&emsp;&emsp;- 代码整理<br>
						&emsp;&emsp;- 修复一些问题</p>
					</p></details>
					<details><summary><span>v8.0.0</span> </summary><p>
						&emsp;&emsp;-(更新来自@User782Tec)<br>
						&emsp;&emsp;-使用svg矢量图优化了任务管理器性能图表的绘制<br>
						&emsp;&emsp;-加入了半成品的Microsoft Store<br>
						&emsp;&emsp;-其他一些细微之处的更新</p>
					</p></details>
					<!-- 全部列出来那这个更多意义何在？ -->
					<a onclick="window.open('changelog.md','_blank');" win12_title="changelog.md" class="a jump" style="text-align: center;">更多</a>
				</div>
			</div>
		</div>
	</div>
	<div class="window notepad" onclick="if(font_window){playWindowsBackground();showwin('notepad-fonts');}">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/notepad.svg" class="icon">
			<p data-i18n="notepad.name">记事本</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('notepad');hidewin('notepad-fonts', 'configs')"><i
						class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('notepad')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('notepad')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/notepad.svg" class="icon">
		</div>
		<div class="content" id="win-notepad">
			<div class="tool">
				<a class="a" onmouseover="showdp(this,'notepad.file',null)" onmouseleave="hidedp()" data-i18n="notepad.file">文件</a>
				<a class="a" onmouseover="showdp(this,'notepad.edit',null)" onmouseleave="hidedp()" data-i18n="notepad.edit">编辑</a>
				<a class="a" onmouseover="showdp(this,'notepad.view',null)" onmouseleave="hidedp()" data-i18n="notepad.format">格式</a>
			</div>
			<div class="text-box" contenteditable="true" spellcheck="false">
				<p>Type here...</p>
			</div>
			<a href="" download="未命名.html" style="opacity: 0;" class="save">另存为</a>
		</div>
	</div>
	<!-- 这个东西还有用吗？？（ -->
	<div class="window notepad-fonts">
		<div class="titbar">
			<img src="icon/notepad.svg" class="icon">
			<p>字体</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('notepad-fonts', 'configs');font_window=false;"><i class="bi bi-x-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/notepad.svg" class="icon">
		</div>
		<div class="content" id="win-notepad-font">
			<div class="row">
				<div class="select-input" id="win-notepad-font-type">
					<div class="description">字体</div>
					<input type="text" />
					<list class="value-box">
						<a class="option">Arial</a>
						<a class="option">Book Antiqua</a>
						<a class="option">Cascadia Code</a>
						<a class="option">Comic Sans MS</a>
						<a class="option">Consolas</a>
						<a class="option">Courier</a>
						<a class="option">cursive</a>
						<a class="option">Geneva</a>
						<a class="option">Georgia</a>
						<a class="option">Ink Free</a>
						<a class="option">Lucida Console</a>
						<a class="option">Microsoft YaHei UI</a>
						<a class="option">Monaco</a>
						<a class="option">monospace</a>
						<a class="option">Palatino Linotype</a>
						<a class="option">serif</a>
						<a class="option">Tahoma</a>
						<a class="option">仿宋</a>
						<a class="option">方正舒体</a>
						<a class="option">华文行楷</a>
						<a class="option">楷体</a>
						<a class="option">宋体</a>
						<a class="option">幼圆</a>
					</list>
				</div>
				<div class="select-input" id="win-notepad-font-size">
					<div class="description">大小</div>
					<input type="text" />
					<list class="value-box">
						<a class="option">初号</a>
						<a class="option">小初</a>
						<a class="option">一号</a>
						<a class="option">小一</a>
						<a class="option">二号</a>
						<a class="option">小二</a>
						<a class="option">三号</a>
						<a class="option">小三</a>
						<a class="option">四号</a>
						<a class="option">小四</a>
						<a class="option">五号</a>
						<a class="option">小五</a>
					</list>
				</div>
				<div class="select-input" id="win-notepad-font-style">
					<div class="description">字形</div>
					<input type="text" />
					<list class="value-box">
						<a class="option">正常</a>
						<a class="option">斜体</a>
						<a class="option">粗体</a>
						<a class="option">粗偏斜体</a>
					</list>
				</div>
			</div>
			<div class="preview">
				<div class="description">
					预览
				</div>
				<div class="preview-box">
					<div>Hello</div>
					<div>你好</div>
					<div>こんにちは</div>
				</div>
			</div>
			<div class="buttons">
				<a class="a submit act" onclick="apps.notepadFonts.commitFont();" data-i18n="ok">确定</a>
				<a class="a cancel act" onclick="hidewin('notepad-fonts', 'configs');font_window=false;" data-i18n="cancel">取消</a>
			</div>
		</div>
	</div>
	<div class="window terminal terminal-apps">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/terminal.svg" class="icon">
			<p data-i18n="terminal.name">终端</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('terminal')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('terminal')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('terminal')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/terminal.svg" class="icon">
		</div>

		<div class="content" id="win-terminal" onmouseup="$('#win-terminal input').focus();" ontouchend="$('#win-terminal input').focus();">
            <pre>
				Microsoft Windows [版本 12.0.39035.7324]
				(c) Microsoft Corporation。保留所有权利。
			</pre>
			<pre class="text-cmd"></pre>
			<pre
				style="display: flex"><span class="prompt">C:\Windows\System32> </span><input type="text" onkeyup="if (event.keyCode == 13) { apps.terminal.run(); }"></pre>
		</div>
	</div>
	<div class="window python terminal-apps">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/python.svg" class="icon">
			<p>Python 3.10.2</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('python')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('python')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('python')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/python.svg" class="icon">
		</div>

		<div class="content" id="win-python" onmouseup="$('#win-python input').focus();" ontouchend="$('#win-python input').focus();">
            <pre>
				Python 3.10.2  [MSC v.1912 64 bit (AMD64)] :: Anaconda, Inc. on win32
				Type "help", "copyright", "credits" or "license" for more information.
			</pre>
			<pre class="text-cmd"></pre>
			<pre>>>> <input type="text" onkeyup="if (event.keyCode == 13) { apps.python.run(); }"></pre>
		</div>
	</div>
	<div class="window edge tabs" onmousemove="if(apps.edge.in_div('over-bar',event)||apps.edge.in_div('edge-path-bar',event)||apps.edge.in_div('edge-titbar',event)){null}else{if(apps.edge.fuls){$('.edge>.titbar').hide();$('.edge>.content>.tool').hide()}}">
		<div class="resize-bar"></div>
		<div style="height:3px;width:100%;display: none;" id="over-bar" onmouseover="if(apps.edge.fuls){$('.edge>.titbar').show();$('.edge>.content>.tool').show()}" onmouseleave="if(apps.edge.fuls&&(apps.edge.b1||apps.edge.b2)){$('.edge>.titbar').hide();$('.edge>.content>.tool').hide()}"></div>
		<div class="titbar" onmouseleave="apps.edge.b1=false;" onmouseover="apps.edge.b1=true;" id="edge-titbar">
			<img src="icon/edge.svg" class="icon">
			<p style="display: none;">Microsoft Edge</p>
			<div class="tabs">
			</div>
			<div>
				<a class="a wbtg red" onclick="hidewin('edge')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('edge');apps.edge.max=!apps.edge.max;" id="edge-max"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="apps.edge.exitfullscreen()" id="fuls-edge-exit" style="display: none;"><i class="bi bi-arrows-angle-contract" id="fuls-edge-i"></i></a>
				<a class="a wbtg" onclick="minwin('edge')"><i class="bi bi-dash-lg"></i></a>
				<a class="a wbtg" onclick="apps.edge.fullscreen()" id="fuls-edge"><i class="bi bi-arrows-angle-expand" id="fuls-edge-i"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/edge.svg" class="icon">
		</div>
		<div class="content" id="win-edge">
			<div class="tool" onmouseleave="apps.edge.b2=false;" onmouseover="apps.edge.b2=true;" id="edge-path-bar">
				<a class="a back" onclick="apps.edge.back(apps.edge.tabs[apps.edge.now][0]);">
					<i class="bi bi-arrow-left-short" style="font-size: 25px;--top:-5px;"></i>
				</a>
				<a class="a front" onclick="apps.edge.front(apps.edge.tabs[apps.edge.now][0]);">
					<i class="bi bi-arrow-right-short" style="font-size: 25px;--top:-5px;"></i>
				</a>
				<a class="a" onclick="apps.edge.reload()">
					<i class="bi bi-arrow-clockwise"></i>
				</a>
				<input type="text" onkeyup="if(event.keyCode==13&&$(this).val()!=''){apps.edge.goto($(this).val())}"
					placeholder="在必应中搜索，或输入一个网址" class="url" spellcheck="false" id="edge-path" data-i18n-attr="placeholder" data-i18n-key="edge.schbing">
				<input type="text" onkeyup="if(event.keyCode==13&&$(this).val()!=''){m_tab.rename('edge',$(this).val());}"
					placeholder="为标签页命名" class="rename" spellcheck="false"  data-i18n-attr="placeholder" data-i18n-key="edge.rename">
				<a class="a">
					<i class="bi bi-info-circle" win12_title="浏览器只能访问允许嵌套的网站。由于规则限制无法获取网页标题，请右键标签页手动命名"></i>
				</a>
				<script>
				function add_save(){
					t = document.getElementById('edge-path').value
					if(t.length>7){
						t = t.substring(0,7) + '...';
					}
          var className = 'save_' + Math.random().toString(36).substr(2);
					document.getElementById('save-bar').innerHTML += "<a class='save-item " + className + "' " + `oncontextmenu="return showcm(event,'save-bar', '${className}');"` + "  onclick='apps.edge.goto(`" + document.getElementById('edge-path').value +  "`)' win12_title='" + document.getElementById('edge-path').value + "' class='save-item'><i class='bi bi-file-earmark'></i>" + t + "</a>";
				}
				</script>
				<a class="a" style="filter: contrast(0.2);" onclick="add_save()">
					<i class="bi bi-star"></i>
				</a>
				<a class="a" style="filter: contrast(0.2);">
					<i class="bi bi-code-slash"></i>
				</a>
		    </div>
			<div id="save-bar" style="margin:10px;margin-top: 3px;white-space: nowrap;overflow-x: scroll;">
				<!--收藏夹-->
				<a onclick="apps.edge.goto('https://tjy-gitnub.github.io/')" win12_title="https://tjy-gitnub.github.io/" class="save-item"><i class="bi bi-file-earmark"></i>星源</a>
				<a onclick="apps.edge.goto('https://www.bing.com')" win12_title="https://www.bing.com" class="save-item"><i class="bi bi-file-earmark"></i>Bing</a>
				<style>
					#save-bar{
						scrollbar-width: none; /* Firefox */
						-ms-overflow-style: none; /* IE 10+ */
					}
					#save-bar::-webkit-scrollbar {
					  display: none; /* Chrome Safari */
					}
				</style>
			</div>
			<iframe frameborder="0" class="0 show" onmousemove="if(apps.edge.fuls){$('.edge>.titbar').hide();$('.edge>.content>.tool').hide()}">
			</iframe>
		</div>
	</div>
	<div class="msg update" oncontextmenu="return showcm(event,'msgupdate',null)">
		<div class="main" onclick="openapp('about');if($('.window.about').hasClass('min'))minwin('about');
			$('#win-about>.about').removeClass('show');$('#win-about>.update').addClass('show');
			$('#win-about>.update>div>details:first-child').attr('open','open')">
			<p class="tit"></p>
			<p class="cont"></p>
		</div>
		<div class="a hide" onclick="$('.msg').removeClass('show')"><i class="bi bi-arrow-right-short"></i></div>
	</div>
	<div class="window vscode webapp">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/vscode.png" class="icon">
			<p>Visual Studio Code</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('vscode')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('vscode')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('vscode')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/vscode.png" class="icon">
		</div>
		<div class="content" id="win-vscode">
			<!-- <iframe src="https://github1s.com/" frameborder="0" style="width: 100%; height: 100%;" loading="lazy"></iframe> -->
		</div>
	</div>
	<div class="window bilibili webapp">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/bilibili.png" class="icon">
			<p data-i18n="bilibili.name">哔哩哔哩</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('bilibili')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('bilibili')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('bilibili')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/bilibili.png" class="icon">
		</div>
		<div class="content" id="win-bilibili">
			<!-- <iframe src="https://bilibili.com/" frameborder="0" style="width: 100%; height: 100%;" loading="lazy"></iframe> -->
		</div>
	</div>
	
	<div class="window copilot">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/copilot.svg" class="icon">
			<p>Windows 12 Copilot</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('copilot')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('copilot')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('copilot')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/copilot.svg" class="icon">
		</div>
		<div class="content" id="win-copilot">
		</div>
	</div>
	<div class="window minesweeper">
      <div class="resize-bar"></div>
        <div class="titbar">
	      <img src="icon/minesweeper.svg" class="icon">
						<p>Windows 12 minesweeper</p>
				 <div>
					<a class="a wbtg red" onclick="hidewin('minesweeper')"><i class="bi bi-x-lg"></i></a>
					 <a class="a wbtg max" onclick="maxwin('minesweeper')"><i class="bi bi-app"></i></a>
			        <a class="a wbtg" onclick="minwin('minesweeper')"><i class="bi bi-dash-lg"></i></a>
				</div>
		            </div>
					       <div class="loadback">
		         <img src="icon/minesweeper.svg" class="icon">
													
					</div>
		        <div class="content" id="win-minesweeper">																														                </div>
		    </div>
	<div class="window wsa">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/wsa.png" class="icon">
			<p data-i18n="wsa.name">适用于 Android™️ 的 Windows 子系统</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('wsa')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" style="pointer-events: none;color: #777;"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('wsa')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/wsa.png" class="icon">
		</div>
		<div class="content" id="win-wsa" style="margin-bottom:auto;">
			<iframe src="https://android11react.osrc.com/" frameborder="0" style=" width: 100%;height: 100%;" loading="lazy"></iframe>
		</div>
	</div>
	<div class="window pythonEditor">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/pythonEditor.svg" class="icon">
			<p>Python Editor</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('pythonEditor')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('pythonEditor')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('pythonEditor')"><i class="bi bi-dash-lg"></i></a>
				<a class="a wbtg run" onclick="apps.pythonEditor.run();"><i class="bi bi-play"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/pythonEditor.svg" class="icon">
		</div>
		<div class="content" id="win-pythonEditor">
			<div id="win-python-ace-editor"></div>
			<div class="output" id="output"></div>
		</div>
	</div>
	<div class="window camera">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/camera.svg" class="icon">
			<p data-i18n="camera.name">相机</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('camera')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('camera')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('camera')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/camera.svg" class="icon">
		</div>
		<div class="content" id="win-camera">
			<div class="video">
				<video></video>
			</div>
			<div class="control">
				<div>
					<div class="startbutton act" onclick="apps.camera.takePhoto();"></div>
				</div>
			</div>
			<canvas style="display: none;"></canvas>
			<a style="display: none;"></a>
		</div>
	</div>
	<div class="window windows12">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/logo.svg" class="icon">
			<p>Windows 12</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('windows12')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('windows12')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('windows12')"><i class="bi bi-dash-lg"></i></a>
				<a class="a wbtg" onclick="apps.windows12.init()"><i class="bi bi-arrow-counterclockwise"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/logo.svg" class="icon">
		</div>
		<div class="content" id="win-win12">
			<iframe style="height:100%; width:100%;object-fit:cover;" id="win12-window" frameborder="no"></iframe>
		</div>
	</div>
	<div class="window camera-notice">
		<div class="titbar">
			<img src="icon/camera.svg" class="icon">
			<p data-i18n="camera.notice">提示</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('camera-notice');hidewin('camera')"><i
						class="bi bi-x-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/camera.svg" class="icon">
		</div>
		<div class="content" id="win-camera-notice" style="padding: 0px 10px 0px 10px;">
			<div style="display: flex; width: 100%; justify-content: center; margin-top: 10px; margin-bottom: 10px;">
				<div
					style="width: 50px; height: 50px; background-image: url('icon/camera.svg'); background-repeat: no-repeat;">
				</div>
			</div>
			<p data-i18n="camera.notice.txt">
				&emsp;&emsp;欢迎使用“相机”应用！本应用由 User782Tec 开发， tjy-gitnub 维护。<br />
				&emsp;&emsp;在使用中，此应用将会获取访问您摄像头的权限（用于进行拍摄照片等操作）。此应用不会将任何您的个人信息（包括所拍摄的照片）上传至服务器，更不会泄露给他人。<br />
				&emsp;&emsp;若您不同意上述协议，则将不能使用此应用。
			</p>
			<div class="buttons">
				<a class="a submit act"
					onclick="hidewin('camera-notice', 'configs');localStorage.setItem('camera', true);openapp('camera');" data-i18n="camera.notice.agree">同意并继续</a>
				<a class="a cancel act" onclick="hidewin('camera-notice')" data-i18n="camera.notice.disag">不同意</a>
			</div>
		</div>
	</div>
	<div class="window run">
		<div class="titbar">
			<img src="icon/run.svg" class="icon">
			<p data-i18n="run.name">运行</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('run')"><i class="bi bi-x-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/run.svg" class="icon">
		</div>
		<div class="content" id="win-run">
			<div class="top">
				<div class="icon"></div>
				<div class="text" data-i18n="run.txt">Windows 将根据你所输入的名称，为你打开相应的程序、文件夹、文档或 Internet 资源。</div>
			</div>
			<div class="open">
				<div class="prompt" data-i18n="run.open">打开: </div>
				<input class="input" type="text"
					onkeyup="if (event.keyCode == 13) { hidewin('run');apps.run.run($('#win-run>.open>input').val()); }">
			</div>
			<div class="buttons">
				<a class="a submit act" onclick="hidewin('run');apps.run.run($('#win-run>.open>input').val())" data-i18n="ok">确定</a>
				<a class="a cancel act" onclick="hidewin('run')" data-i18n="cancel">取消</a>
			</div>
		</div>
	</div>
	<div class="window whiteboard">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/whiteboard.svg" class="icon">
			<p>Whiteboard</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('whiteboard')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('whiteboard')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('whiteboard')"><i class="bi bi-dash-lg"></i></a>
				<a class="a wbtg" onclick="apps.whiteboard.download()"><i class="bi bi-download"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/whiteboard.svg" class="icon">
		</div>
		<div class="content" id="win-whiteboard">
			<a class="download" href="" download="Picture.png"></a>
			<canvas onmousedown="apps.whiteboard.draw(event);" ontouchstart="apps.whiteboard.draw(event);"></canvas>
			<div class="toolbar">
				<div class="tools">
					<div class="pen1 active" onclick="apps.whiteboard.changePen.call(this)" data-color="red">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="red"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="pen2" onclick="apps.whiteboard.changePen.call(this)" data-color="orange">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="orange"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="pen3" onclick="apps.whiteboard.changePen.call(this)" data-color="yellow">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="yellow"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="pen4" onclick="apps.whiteboard.changePen.call(this)" data-color="green">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="green"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="pen5" onclick="apps.whiteboard.changePen.call(this)" data-color="blue">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="blue"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="pen6" onclick="apps.whiteboard.changePen.call(this)" data-color="purple">
						<svg xmlns="https://www.w3.org/2000/svg" width="76" height="75" viewBox="0 0 760 950"
							color="#e92a2a">
							<path
								d="M120 881c0-155 96-575 144-630 7-9 25-48 40-88 31-82 50-104 71-82 14 15 81 162 120 264 51 136 105 412 105 542v63h-30c-30 0-30-1-30-55v-55H180v55c0 54 0 55-30 55h-30v-69zm343-98l107-6v-44c0-84-95-447-122-464-15-10-148-12-172-3-27 10-114 328-123 452l-6 72h105c58 0 153-3 211-7zm-89-588c-4-8-10-15-15-15s-9 7-9 15 7 15 15 15c9 0 12-6 9-15z"
								fill="#020202"></path>
							<path fill="purple"
								d="M150 880v-70h420v140H150v-70zM305 213c10-42 47-123 55-123 9 0 60 112 60 134 0 13-11 16-61 16h-61l7-27z">
							</path>
						</svg>
					</div>
					<div class="eraser" onclick="apps.whiteboard.changePen.call(this)" data-color="eraser">
						<img src="apps/icons/whiteboard/marker.png" height="70">
					</div>
					<div class="delete" onclick="apps.whiteboard.delete()">
						<img src="apps/icons/whiteboard/delete.png" height="60">
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="window winver">
		<div class="titbar">
			<p data-i18n="winver.name">关于 Windows</p>
			<div>
				<a class="a wbtg red" onclick="hidewin('winver');"><i class="bi bi-x-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
		</div>
		<div class="content" id="win-winver">
			<div class="logo">
				<div class="img"></div>
				<p>Windows 12</p>
			</div>
			<hr>
			<p>Microsoft Windows</p>
			<p data-i18n="winver.p1">版本 25H2 (OS 内部版本 24612.1896)</p>
			<p data-i18n="winver.p2">© Microsoft Corporation。保留所有权利。</p>
			<p style="margin-top: 20px" data-i18n="winver.p3">Windows12 专业版操作系统及其用户界面受美国和其他国家/地区的商标法和其他待颁布或已颁布的知识产权法保护。</p>
			<div class="bottom">
				<p data-i18n="winver.p4">根据Microsoft软件许可条款，许可如下用户使用本产品</p>
				<p style="margin:20px 0 0 90px">Administrator</p>
			</div>
			<p ondblclick="$(this).hide()" class="a mesg" data-i18n="winver.p5">上面都是胡扯，切勿当真(双击隐藏此消息)</p>
			<div class="buttons">
				<a class="a submit act" onclick="hidewin('winver');" data-i18n="ok">确定</a>
			</div>
		</div>
	</div>
	<div class="window taskmgr">
		<div class="resize-bar"></div>
		<div class="titbar">
			<img src="icon/taskmgr.png" class="icon">
			<span style="font-size: 15px;margin-top: 9px;" data-i18n="taskmgr.name">任务管理器</span>
			<div style="flex-grow: 1;display: flex;align-items: center;justify-content: center;">
				<input type="text" class="input" style="width: 75%;" placeholder="键入要搜索的名称" id="tsk-search" oninput="apps.taskmgr.generateProcesses()" value=""></input>
			</div>
			<div>
				<a class="a wbtg red" onclick="hidewin('taskmgr')"><i class="bi bi-x-lg"></i></a>
				<a class="a wbtg max" onclick="maxwin('taskmgr')"><i class="bi bi-app"></i></a>
				<a class="a wbtg" onclick="minwin('taskmgr')"><i class="bi bi-dash-lg"></i></a>
			</div>
		</div>
		<div class="loadback">
			<img src="icon/taskmgr.png" class="icon">
		</div>
		<div class="content" id="win-taskmgr" style="height:100%">
			<div class="menu" style="height:100%; display: flex; flex-direction: column;">
				<a class="fold" onclick="apps.taskmgr.fold()" win12_title="折叠"><i class="bi bi-list"></i></a>
				<list class="focs">
					<a class="processes check" onclick="apps.taskmgr.page('processes')" win12_title="进程"><icon class="t-icon"></icon><p><span>进程</span></p></a>
					<a class="performance" onclick="apps.taskmgr.page('performance')" win12_title="性能"><icon class="s-icon"></icon><p><span>性能</span></p></a>
					<a class="setting" onclick="apps.taskmgr.page('setting')" style="position: absolute; bottom: 0px;" win12_title="设置"><i class="bi bi-gear"></i><p><span>设置</span></p></a>
					<span class="focs" data-type="abs" style="position: absolute;top: -445px; left: 0px;"></span>
				</list>
			</div>
			<div class="main">
				<div class="cnt processes show">
					<div class="tit">
						<p>进程</p>
						<hr>
					</div>
					<table>
						<thead>
							<tr class="title">
								<th>进程名称</th>
								<th class="align-right" onclick="apps.taskmgr.changeSort(this.childNodes[0], 'cpu');"><i class="bi bi-chevron-down"></i><div class="value">11.4%</div>CPU</th>
								<th class="align-right" onclick="apps.taskmgr.changeSort(this.childNodes[0], 'memory');"><i class="bi"></i><div class="value">11.4%</div>内存</th>
								<th class="align-right" onclick="apps.taskmgr.changeSort(this.childNodes[0], 'disk');"><i class="bi"></i><div class="value">11.4%</div>硬盘</th>
								<th class="power">电源使用情况</th>
							</tr>
						</thead>
						<tbody class="view">
							<!-- 进程信息 -->
						</tbody>
					</table>
				</div>
				<div class="cnt 404">
					<div class="content" style="display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; flex-direction: column; text-align: center">
						<p><b>无筛选结果</b><br />请尝试使用其他名称、发布者或 PID 键入以进行筛选。</p>
						<br />
						<a onclick="$('#tsk-search').val('');apps.taskmgr.generateProcesses()" style="cursor: pointer;color:var(--href);">重置筛选器</a>
					</div>
				</div>
				<div class="cnt performance">
					<div class="tit">
						<p>性能</p>
						<hr>
					</div>
					<div class="content">
						<div class="select-menu">
							<div onclick="apps.taskmgr.graph('graph-cpu')" class="graph-cpu check">
								<div class="left">
									<svg class="graph-view-cpu" viewBox="0 0 6000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="right">
									<div class="tit">CPU</div>
									<div class="data">
										<div class="value1">11.4%</div>
										<div class="value2">10GHz</div>
									</div>
								</div>
							</div>
							<div onclick="apps.taskmgr.graph('graph-memory');" class="graph-memory">
								<div class="left">
									<svg class="graph-view-memory" viewBox="0 0 6000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="right">
									<div class="tit">内存</div>
									<div class="data">
										<div class="value1">11.4 / 100GB</div>
										<div class="value2">11.4%</div>
									</div>
								</div>
							</div>
							<div onclick="apps.taskmgr.graph('graph-disk');" class="graph-disk">
								<div class="left">
									<svg class="graph-view-disk" viewBox="0 0 6000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="right">
									<div class="tit">磁盘</div>
									<div class="data">
										<div class="tit">SSD</div>
										<div class="value2">1.14%</div>
									</div>
								</div>
							</div>
							<div onclick="apps.taskmgr.graph('graph-wifi');" class="graph-wifi">
								<div class="left">
									<svg class="graph-view-wifi" viewBox="0 0 6000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="right">
									<div class="tit">Wi-Fi</div>
									<div class="data">
										<div class="tit">WLAN</div>
										<div class="value2">发送: 1145 接收: 1145 Mbps</div>
									</div>
								</div>
							</div>
							<div onclick="apps.taskmgr.graph('graph-gpu');" class="graph-gpu">
								<div class="left">
									<svg class="graph-view-gpu" viewBox="0 0 6000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="right">
									<div class="tit">GPU 0</div>
									<div class="data">
										<div class="tit">NBidia GeForce RTX 4090Ti</div>
										<div class="value2">1%</div>
									</div>
								</div>
							</div>
						</div>
						<div class="performance-graph">
							<div class="graph-cpu show">
								<div class="tit">
									<div class="left-name">CPU</div>
									<div class="right-message">The Wandering Earth (R) NB (R) Quantum Chip 550W @ 8192</div>
								</div>
								<div class="graph-msg top">
									<div class="left">% 利用率</div>
									<div class="right">100%</div>
								</div>
								<div class="graph" win12_title="CPU活动">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="graph-msg bottom">
									<div class="left">60 秒</div>
									<div class="right">0</div>
								</div>
								<div class="information">
									<div class="left">
										<div><div class="top">利用率</div><div class="value">11.4%</div></div>
										<div><div class="top">速度</div><div class="value">1024.53 GHz</div></div>
										<div><div class="top">进程</div><div class="value">1145</div></div>
										<div><div class="top">线程</div><div class="value">114514</div></div>
										<div><div class="top">句柄</div><div class="value">114514191918</div></div>
										<div><div class="top">正常运行时间</div><div class="value">0:11:45:14</div></div>
									</div>
									<div class="right">
										<table>
											<tbody>
												<tr><td>基准速度: </td><td>10.0 GHz</td></tr>
												<tr><td>插槽: </td><td>1</td></tr>
												<tr><td>内核: </td><td>32</td></tr>
												<tr><td>逻辑处理器: </td><td>32</td></tr>
												<tr><td>虚拟化: </td><td>已启用</td></tr>
												<tr><td>L1缓存: </td><td>512 MB</td></tr>
												<tr><td>L2缓存: </td><td>2.1 GB</td></tr>
												<tr><td>L3缓存: </td><td>5.5 GB</td></tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<div class="graph-memory">
								<div class="tit">
									<div class="left-name">内存</div>
									<div class="right-message">100GB</div>
								</div>
								<div class="graph-msg top">
									<div class="left">内存使用量</div>
									<div class="right">100GB</div>
								</div>
								<div class="graph" win12_title="使用中">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="graph-msg bottom">
									<div class="left">60 秒</div>
									<div class="right">0</div>
								</div>
								<div class="graph-msg top">
									<div class="left">内存组合</div>
								</div>
								<div class="graph2 minor" style="margin-bottom: 10px; border: 1px #660099 solid; height: 35px !important">
									<div class="chart" style="background-color: #66009922;"></div>
								</div>
								<div class="information">
									<div class="left">
										<div><div class="top">使用中(已压缩)</div><div class="value">11.4 GB</div></div>
										<div><div class="top">可用</div><div class="value">88.6 GB</div></div>
										<div><div class="top">已提交</div><div class="value">12 / 100 GB</div></div>
										<div><div class="top">已缓存</div><div class="value">5.5 GB</div></div>
										<div><div class="top">分页缓冲池</div><div class="value">1 GB</div></div>
										<div><div class="top">非分页缓冲池</div><div class="value">365 MB</div></div>
									</div>
									<div class="right">
										<table>
											<tbody>
												<tr><td>速度: </td><td>4514MHz</td></tr>
												<tr><td>已使用插槽: </td><td>2 / 2</td></tr>
												<tr><td>外形规格: </td><td>Row of chips</td></tr>
												<tr><td>为硬件保留的内存：</td><td>200MB</td></tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<div class="graph-disk">
								<div class="tit">
									<div class="left-name">磁盘 0 (C: D:)</div>
									<div class="right-message">NB++ 360G</div>
								</div>
								<div class="graph-msg top">
									<div class="left">活动时间</div>
									<div class="right">100%</div>
								</div>
								<div class="graph" win12_title="磁盘处理读取或写入请求的时间百分比">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="graph-msg bottom">
									<div class="left">60 秒</div>
									<div class="right">0</div>
								</div>
								<div class="graph-msg top">
									<div class="left">磁盘传输速率</div>
									<div class="right">100 MB/秒</div>
								</div>
								<div class="graph2 minor" style="margin-bottom: 10px; height: 70px !important; min-height: 70px !important;">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg" style="width: 100% !important; height: 100% !important"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="information">
									<div class="left">
										<div><div class="top">活动时间</div><div class="value">1.14%</div></div>
										<div><div class="top">平均响应时间</div><div class="value">0.014 毫秒</div></div>
										<div style="border-left: 2px #008000 solid; padding-left: 10px;"><div class="top">读取速度</div><div class="value">14 KB/秒</div></div>
										<div style="border-left: 2px #008000 dotted; padding-left: 10px;"><div class="top">写入速度</div><div class="value">114 KB/秒</div></div>
									</div>
									<div class="right">
										<table>
											<tbody>
												<tr><td>容量: </td><td>359 GB</td></tr>
												<tr><td>已格式化: </td><td>359 GB</td></tr>
												<tr><td>系统磁盘: </td><td>是</td></tr>
												<tr><td>页面文件: </td><td>是</td></tr>
												<tr><td>类型: </td><td>SSD</td></tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<div class="graph-wifi">
								<div class="tit">
									<div class="left-name">Wi-Fi</div>
									<div class="right-message">NB Wireless LAN 802.11ac</div>
								</div>
								<div class="graph-msg top">
									<div class="left">吞吐量</div>
									<div class="right">100 Mbps</div>
								</div>
								<div class="graph" win12_title="此网络上的发送和接收活动">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="graph-msg bottom">
									<div class="left">60 秒</div>
									<div class="right">0</div>
								</div>
								<div class="information">
									<div class="left">
										<div><div class="top">发送</div><div class="value">114 Mbps</div></div>
										<div><div class="top">接收</div><div class="value">114 Mbps</div></div>
									</div>
									<div class="right">
										<table>
											<tbody>
												<tr>
													<td>适配器名称: </td>
													<td>WLAN</td>
												</tr>
												<tr>
													<td>SSID: </td>
													<td>NB Network</td>
												</tr>
												<tr>
													<td>连接类型: </td>
													<td>802.11ac</td>
												</tr>
												<tr>
													<td>IPv4地址: </td>
													<td>114.514.19.81</td>
												</tr>
												<tr>
													<td>IPv6地址: </td>
													<td>2CBB::2CBB:2CBB:2CBB:2CBB</td>
												</tr>
												<tr>
													<td>信号强度: </td>
													<td>
														<svg xmlns="http://www.w3.org/2000/svg" viewBox="4.4 4.4 26.2 31.2" width="1.45em" height="1.4em">
															<path d="M 5 25 L 5 35 L 10 35 L 10 25 L 5 25 M 10 20 L 10 35 L 15 35 L 15 20 L 10 20 M 15 15 L 20 15 L 20 35 L 15 35 L 15 15 M 20 10 L 25 10 L 25 35 L 20 35 L 20 10 M 25 5 L 25 35 L 30 35 L 30 5 L 25 5" stroke="#8e5829" stroke-width="0.6" fill="#8e582988"/>
														</svg>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<div class="graph-gpu">
								<div class="tit">
									<div class="left-name">GPU</div>
									<div class="right-message">NBidia GeForce RTX 9090Ti</div>
								</div>
								<div class="graphs">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="usage" style="display: none;" xmlns="http://www.w3.org/2000/svg"></svg>
									<div class="graph1">
										<div class="title-gpu">3D</div>
										<div class="chart">
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
										</div>
									</div>
									<div class="graph2">
										<div class="title-gpu">Copy</div>
										<div class="chart">
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
										</div>
									</div>
									<div class="graph3">
										<div class="title-gpu">Video Decode</div>
										<div class="chart">
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
										</div>
									</div>
									<div class="graph4">
										<div class="title-gpu">Video Processing</div>
										<div class="chart">
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
											<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
										</div>
									</div>
								</div>
								<div class="graph-msg top">
									<div class="left">专用 GPU 内存利用率</div>
									<div class="right">16 GB</div>
								</div>
								<div class="graph gpu2-1 minor" style="height: 70px !important; min-height: 70px !important;" win12_title="专用 GPU 内存利用率">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="graph-msg top">
									<div class="left">共享 GPU 内存利用率</div>
									<div class="right">32 GB</div>
								</div>
								<div class="graph gpu2-2 minor" style="height: 70px !important; min-height: 70px !important; margin-bottom: 10px" win12_title="共享 GPU 内存利用率">
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="chart" xmlns="http://www.w3.org/2000/svg"></svg>
									<svg viewBox="0 0 6000 1000" preserveAspectRatio="none" class="bg" xmlns="http://www.w3.org/2000/svg"></svg>
								</div>
								<div class="information">
									<div class="left">
										<div><div class="top">利用率</div><div class="value">1.1%</div></div>
										<div><div class="top">专用 GPU 内存</div><div class="value">0.1 GB / 2 GB</div></div>
										<div><div class="top">共享 GPU 内存</div><div class="value">1.1 GB / 16 GB</div></div>
										<div><div class="top">GPU 内存</div><div class="value">1.2 / 18 GB</div></div>
									</div>
									<div class="right">
										<table>
											<tbody>
												<tr>
													<td>驱动程序版本: </td>
													<td>11.45.14.19</td>
												</tr>
												<tr>
													<td>驱动程序日期: </td>
													<td>2025 / 1 / 5</td>
												</tr>
												<tr>
													<td>DirectX 版本</td>
													<td>12 (FL 12.1)</td>
												</tr>
												<tr>
													<td>物理位置: </td>
													<td>PCI 总线 0、设备 14 、功能 0</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="cnt setting">
					<div class="tit">
						<p>设置</p>
						<hr>
					</div>
					<div>
						<h3>窗口管理</h3>
						<input type="checkbox" id="tsk-setting-topmost" name="tsk-setting-topmost" style="height: 15px;width:15px;color:#fff;" onclick="if(checked){topmost[topmost.length]='taskmgr'}else{topmost.splice(topmost.indexOf('taskmgr',1))};hidewin('taskmgr');apps.taskmgr.page('processes');$('#win-taskmgr>.menu>list.focs>a')[0].click();saveDesktop();$('#tsk-search').val('');setTimeout('openapp(`taskmgr`);apps.taskmgr.generateProcesses();',300);"/><label for="tsk-setting-topmost" style="font-size: 15px;">置于顶层</label>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script defer src="data/tasks.js"></script>
	<script defer src="scripts/utils.js"></script>

	<script defer src="module/apps.js"></script>
	<script defer src="desktop.js"></script>
	<script defer src="module/window.js"></script>
	<script defer src="module/widget.js"></script>
	<script defer src="module/tab.js"></script>

	<script defer src="scripts/setting_getTime.js"></script><!-- 设置页面获取时间逻辑 @Junchen Yi 2023-9-17-->
	<script defer src="scripts/news.js"></script>
	<script defer src="https://fastly.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js" async></script>
	<script defer src="https://cdn.staticfile.org/ace/1.23.0/ace.min.js"></script>
	<script defer src="https://cdn.staticfile.org/ace/1.23.0/ext-language_tools.min.js"></script>
	<script defer src="https://cdn.staticfile.org/ace/1.23.0/ext-error_marker.min.js"></script>
	<script defer src='https://cdn.staticfile.org/big.js/6.2.1/big.min.js'></script>
	<script defer src="./scripts/calculator_kernel.js"></script>
	<script defer src='https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js'></script>

</body>

</html>
